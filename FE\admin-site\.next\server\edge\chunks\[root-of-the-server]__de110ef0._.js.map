{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport type { NextRequest } from \"next/server\";\r\nimport { getToken } from \"next-auth/jwt\";\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n  \r\n  // Allow access to login page, root page, and NextAuth API routes\r\n  if (pathname.startsWith(\"/login\") || pathname.startsWith(\"/api/auth\") || pathname === \"/\") {\r\n    console.log(`[Middleware] Allowing public path: ${pathname}`);\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Get the token using next-auth/jwt which works in middleware\r\n  const token = await getToken({\r\n    req: request,\r\n    secret: process.env.NEXTAUTH_SECRET\r\n  });\r\n\r\n  console.log(`[Middleware] Token status:`, { \r\n    hasToken: !!token, \r\n    tokenId: token?.id,\r\n    tokenEmail: token?.email \r\n  });\r\n\r\n  // If there's no token (user is not authenticated), redirect to login\r\n  if (!token) {\r\n    console.log(`[Middleware] No token found, redirecting to login`);\r\n    const loginUrl = new URL(\"/login\", request.url);\r\n    // Optionally, add a callbackUrl to redirect back after login\r\n    loginUrl.searchParams.set(\"callbackUrl\", pathname);\r\n    return NextResponse.redirect(loginUrl);\r\n  }\r\n\r\n  console.log(`[Middleware] Token found, allowing access to: ${pathname}`);\r\n  // If there is a token, allow the request to proceed\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  // Match all routes except for static files, _next internal routes, favicon, and public routes\r\n  matcher: [\r\n    \"/((?!api/auth|_next/static|_next/image|favicon.ico|login|$).*)\",\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iEAAiE;IACjE,IAAI,SAAS,UAAU,CAAC,aAAa,SAAS,UAAU,CAAC,gBAAgB,aAAa,KAAK;QACzF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,UAAU;QAC5D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,8DAA8D;IAC9D,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,KAAK;QACL,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACrC;IAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,CAAC,EAAE;QACxC,UAAU,CAAC,CAAC;QACZ,SAAS,OAAO;QAChB,YAAY,OAAO;IACrB;IAEA,qEAAqE;IACrE,IAAI,CAAC,OAAO;QACV,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;QAC/D,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,6DAA6D;QAC7D,SAAS,YAAY,CAAC,GAAG,CAAC,eAAe;QACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,UAAU;IACvE,oDAAoD;IACpD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,8FAA8F;IAC9F,SAAS;QACP;KACD;AACH"}}]}