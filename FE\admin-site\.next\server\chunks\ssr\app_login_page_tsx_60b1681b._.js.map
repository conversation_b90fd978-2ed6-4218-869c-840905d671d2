{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/app/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// Temporarily disable NextAuth to debug\r\nexport default function LoginPage() {\r\n  return (\r\n    <div className=\"flex min-h-screen items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8\">\r\n      <div className=\"w-full max-w-md space-y-8\">\r\n        <div className=\"text-center\">\r\n          <h2 className=\"text-3xl font-bold tracking-tight\">DecorStore Admin</h2>\r\n          <p className=\"mt-2 text-sm text-muted-foreground\">\r\n            NextAuth temporarily disabled for debugging\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;;;;;;AAO5D", "debugId": null}}]}