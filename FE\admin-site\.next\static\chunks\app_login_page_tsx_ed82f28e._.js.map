{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/app/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { Eye, EyeOff } from \"lucide-react\";\r\nimport { signIn } from \"next-auth/react\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\n\r\nconst loginSchema = z.object({\r\n  email: z.string().email(\"Invalid email address\"),\r\n  password: z.string().min(1, \"Password is required\"),\r\n});\r\n\r\ntype LoginFormData = z.infer<typeof loginSchema>;\r\n\r\nexport default function LoginPage() {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const callbackUrl = searchParams.get(\"callbackUrl\") || \"/dashboard\";\r\n  const { error: toastError, success } = useToast();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n  } = useForm<LoginFormData>({\r\n    resolver: zodResolver(loginSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: LoginFormData) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    console.log(\"Login attempt:\", { email: data.email, callbackUrl });\r\n\r\n    try {\r\n      const result = await signIn(\"credentials\", {\r\n        redirect: false,\r\n        email: data.email,\r\n        password: data.password,\r\n        callbackUrl: callbackUrl,\r\n      });\r\n\r\n      console.log(\"Login result:\", result);\r\n\r\n      if (result?.error) {\r\n        const errorMessage = result.error === \"CredentialsSignin\"\r\n          ? \"Invalid email or password\"\r\n          : result.error;\r\n        console.error(\"Login error:\", errorMessage);\r\n        toastError({\r\n          title: \"Error\",\r\n          description: errorMessage,\r\n        });\r\n        setError(errorMessage);\r\n        setIsLoading(false);\r\n      } else if (result?.ok) {\r\n        console.log(\"Login successful, redirecting to:\", callbackUrl);\r\n        success({\r\n          title: \"Success\",\r\n          description: \"Login successful! Redirecting...\",\r\n        });\r\n\r\n        setTimeout(() => {\r\n          router.push(callbackUrl);\r\n        }, 100);\r\n      } else {\r\n        const errorMessage = \"An unexpected error occurred. Please try again.\";\r\n        console.error(\"Unexpected login result:\", result);\r\n        toastError({\r\n          title: \"Error\",\r\n          description: errorMessage,\r\n        });\r\n        setError(errorMessage);\r\n        setIsLoading(false);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = \"Login failed. Please check your connection and try again.\";\r\n      console.error(\"Login catch error:\", error);\r\n      toastError({\r\n        title: \"Error\",\r\n        description: errorMessage,\r\n      });\r\n      setError(errorMessage);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8\">\r\n      <div className=\"w-full max-w-md space-y-8\">\r\n        <div className=\"text-center\">\r\n          <h2 className=\"text-3xl font-bold tracking-tight\">DecorStore Admin</h2>\r\n          <p className=\"mt-2 text-sm text-muted-foreground\">\r\n            Login to manage your store\r\n          </p>\r\n        </div>\r\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\r\n          {error && (\r\n            <div className=\"rounded-lg bg-destructive/10 p-3 text-sm text-destructive\">\r\n              {error}\r\n            </div>\r\n          )}\r\n          <div className=\"space-y-4 rounded-lg border bg-card p-6 shadow-sm\">\r\n            <div className=\"space-y-2\">\r\n              <label\r\n                htmlFor=\"email\"\r\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n              >\r\n                Email\r\n              </label>\r\n              <input\r\n                id=\"email\"\r\n                type=\"email\"\r\n                {...register(\"email\")}\r\n                className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\r\n                placeholder=\"<EMAIL>\"\r\n              />\r\n              {errors.email && (\r\n                <p className=\"text-sm text-destructive\">{errors.email.message}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <label\r\n                htmlFor=\"password\"\r\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n              >\r\n                Password\r\n              </label>\r\n              <div className=\"relative\">\r\n                <input\r\n                  id=\"password\"\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  {...register(\"password\")}\r\n                  className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                  className=\"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground\"\r\n                >\r\n                  {showPassword ? (\r\n                    <EyeOff className=\"h-4 w-4\" />\r\n                  ) : (\r\n                    <Eye className=\"h-4 w-4\" />\r\n                  )}\r\n                </button>\r\n              </div>\r\n              {errors.password && (\r\n                <p className=\"text-sm text-destructive\">\r\n                  {errors.password.message}\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <button\r\n            type=\"submit\"\r\n            disabled={isLoading}\r\n            className=\"flex w-full items-center justify-center rounded-lg bg-primary px-4 py-2 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\r\n          >\r\n            {isLoading ? \"Logging in...\" : \"Login\"}\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,MAAM,cAAc,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;IACvD,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE9C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,QAAQ,GAAG,CAAC,kBAAkB;YAAE,OAAO,KAAK,KAAK;YAAE;QAAY;QAE/D,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,UAAU;gBACV,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,IAAI,QAAQ,OAAO;gBACjB,MAAM,eAAe,OAAO,KAAK,KAAK,sBAClC,8BACA,OAAO,KAAK;gBAChB,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,WAAW;oBACT,OAAO;oBACP,aAAa;gBACf;gBACA,SAAS;gBACT,aAAa;YACf,OAAO,IAAI,QAAQ,IAAI;gBACrB,QAAQ,GAAG,CAAC,qCAAqC;gBACjD,QAAQ;oBACN,OAAO;oBACP,aAAa;gBACf;gBAEA,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,MAAM,eAAe;gBACrB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,WAAW;oBACT,OAAO;oBACP,aAAa;gBACf;gBACA,SAAS;gBACT,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe;YACrB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW;gBACT,OAAO;gBACP,aAAa;YACf;YACA,SAAS;YACT,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;8BAIpD,6LAAC;oBAAK,WAAU;oBAAiB,UAAU,aAAa;;wBACrD,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAGL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,QAAQ;4CACrB,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAGjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAM,eAAe,SAAS;oDAC7B,GAAG,SAAS,WAAW;oDACxB,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDACV,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAKhC,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GA1JwB;;QAIP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAEG,wHAAA,CAAA,WAAQ;QAM3C,iKAAA,CAAA,UAAO;;;KAbW", "debugId": null}}]}