{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\r\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\r\nimport { JWT } from \"next-auth/jwt\";\r\nimport { getServerSession } from \"next-auth/next\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5000\";\r\n\r\ninterface BackendUser {\r\n  id: number;\r\n  username: string;\r\n  email: string;\r\n  role: string;\r\n}\r\n\r\ninterface AuthResponse {\r\n  token: string;\r\n  user: BackendUser;\r\n}\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        email: { label: \"Email\", type: \"email\" },\r\n        password: { label: \"Password\", type: \"password\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null;\r\n        }\r\n\r\n        try {\r\n          const response = await fetch(`${API_URL}/api/Auth/login`, {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({\r\n              email: credentials.email,\r\n              password: credentials.password,\r\n            }),\r\n          });\r\n\r\n          if (!response.ok) {\r\n            const errorData = await response.json();\r\n            throw new Error(errorData.message || \"Login failed\");\r\n          }\r\n\r\n          const data: AuthResponse = await response.json();\r\n\r\n          if (data.token && data.user) {\r\n            return {\r\n              id: data.user.id.toString(), // Convert to string for NextAuth compatibility\r\n              email: data.user.email,\r\n              name: data.user.username, // Use username from API\r\n              role: data.user.role,\r\n              accessToken: data.token,\r\n            };\r\n          }\r\n          return null;\r\n        } catch (error) {\r\n          console.error(\"Authorize error:\", error);\r\n          // Return null if authentication fails or throw an error\r\n          // For a better UX, you might want to throw a specific error that can be caught on the client-side\r\n          throw new Error(error instanceof Error ? error.message : \"Invalid credentials\");\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  session: {\r\n    strategy: \"jwt\",\r\n    maxAge: 30 * 24 * 60 * 60, // 30 days\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user, account }: { token: JWT; user?: any; account?: any }): Promise<JWT> {\r\n      if (account && user) {\r\n        // This is the first login\r\n        token.accessToken = user.accessToken;\r\n        token.id = user.id;\r\n        token.role = user.role; // Add role to the token\r\n        token.email = user.email;\r\n        token.name = user.name;\r\n      }\r\n      return token;\r\n    },\r\n    async session({ session, token }: { session: any; token: JWT }): Promise<any> {\r\n      if (session.user) {\r\n        session.user.accessToken = token.accessToken as string;\r\n        session.user.id = token.id as string;\r\n        session.user.role = token.role as string; // Add role to the session\r\n        // The default session already includes user.name, user.email, user.image\r\n        // Ensure these are populated if they come from the token\r\n        if (token.name) session.user.name = token.name as string;\r\n        if (token.email) session.user.email = token.email as string;\r\n      }\r\n      return session;\r\n    },\r\n  },\r\n  pages: {\r\n    signIn: \"/login\",\r\n    // error: '/auth/error', // Custom error page (optional)\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET, // A secret is required for JWT\r\n  debug: false, // Temporarily disable debug\r\n};\r\n\r\n// Helper function to get the session on the server side\r\nexport const getServerAuthSession = () => getServerSession(authOptions);\r\n\r\n// This is a separate function that can be used in middleware\r\nexport const auth = authOptions;\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;AAEA,MAAM,UAAU,6DAAmC;AAc5C,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,CAAC,EAAE;wBACxD,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,OAAO,YAAY,KAAK;4BACxB,UAAU,YAAY,QAAQ;wBAChC;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;wBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;oBACvC;oBAEA,MAAM,OAAqB,MAAM,SAAS,IAAI;oBAE9C,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;wBAC3B,OAAO;4BACL,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,QAAQ;4BACzB,OAAO,KAAK,IAAI,CAAC,KAAK;4BACtB,MAAM,KAAK,IAAI,CAAC,QAAQ;4BACxB,MAAM,KAAK,IAAI,CAAC,IAAI;4BACpB,aAAa,KAAK,KAAK;wBACzB;oBACF;oBACA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,wDAAwD;oBACxD,kGAAkG;oBAClG,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3D;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAA6C;YAC3E,IAAI,WAAW,MAAM;gBACnB,0BAA0B;gBAC1B,MAAM,WAAW,GAAG,KAAK,WAAW;gBACpC,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE,wBAAwB;gBAChD,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAgC;YAC5D,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,EAAY,0BAA0B;gBACpE,yEAAyE;gBACzE,yDAAyD;gBACzD,IAAI,MAAM,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9C,IAAI,MAAM,KAAK,EAAE,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;YACnD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;AACT;AAGO,MAAM,uBAAuB,IAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE;AAGpD,MAAM,OAAO", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport { authOptions } from \"@/lib/auth\";\r\n\r\n// Create the handler with the authOptions\r\nconst handler = NextAuth(authOptions);\r\n\r\n// Export the handler for GET and POST requests\r\nexport { handler as GET, handler as POST };"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,0CAA0C;AAC1C,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,6GAAA,CAAA,cAAW", "debugId": null}}]}