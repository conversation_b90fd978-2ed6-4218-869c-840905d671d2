(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatCurrency": (()=>formatCurrency),
    "getImageUrl": (()=>getImageUrl),
    "isEmptyString": (()=>isEmptyString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(value) {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD"
    }).format(value);
}
function getImageUrl(path) {
    const baseUrl = ("TURBOPACK compile-time value", "http://localhost:5000/uploads/") || '';
    // If path is null, undefined or empty string, return default image or empty string
    if (!path) {
        return ''; // Or return a default image URL
    }
    // If path is already a full URL (starts with http:// or https://), return as is
    if (path.startsWith('http://') || path.startsWith('https://')) {
        return path;
    }
    // Check duplicate /uploads path
    path.replace('/uploads/uploads', '/uploads');
    // If path starts with /, remove it to avoid duplication with baseUrl
    const normalizedPath = path.startsWith('/') ? path.substring(1) : path;
    // Combine baseUrl and path
    return `${baseUrl}${normalizedPath}`;
}
function isEmptyString(value) {
    return value === null || value === undefined || value.trim() === '';
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/providers/ToastProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ToastProvider": (()=>ToastProvider),
    "useToast": (()=>useToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
"use client";
;
;
;
const ToastContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useToast = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ToastContext);
    if (!context) {
        throw new Error("useToast must be used within a ToastProvider");
    }
    return context;
};
_s(useToast, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const ToastProvider = ({ children })=>{
    _s1();
    const [toasts, setToasts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [counter, setCounter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const addToast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ToastProvider.useCallback[addToast]": (toast)=>{
            const id = `toast-${Date.now()}-${counter}`;
            setCounter({
                "ToastProvider.useCallback[addToast]": (prev)=>prev + 1
            }["ToastProvider.useCallback[addToast]"]);
            setToasts({
                "ToastProvider.useCallback[addToast]": (prevToasts)=>[
                        ...prevToasts,
                        {
                            id,
                            ...toast
                        }
                    ]
            }["ToastProvider.useCallback[addToast]"]);
            return id;
        }
    }["ToastProvider.useCallback[addToast]"], [
        counter
    ]);
    const removeToast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ToastProvider.useCallback[removeToast]": (id)=>{
            setToasts({
                "ToastProvider.useCallback[removeToast]": (prevToasts)=>prevToasts.filter({
                        "ToastProvider.useCallback[removeToast]": (toast)=>toast.id !== id
                    }["ToastProvider.useCallback[removeToast]"])
            }["ToastProvider.useCallback[removeToast]"]);
        }
    }["ToastProvider.useCallback[removeToast]"], []);
    const updateToast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ToastProvider.useCallback[updateToast]": (id, toast)=>{
            setToasts({
                "ToastProvider.useCallback[updateToast]": (prevToasts)=>prevToasts.map({
                        "ToastProvider.useCallback[updateToast]": (t)=>t.id === id ? {
                                ...t,
                                ...toast
                            } : t
                    }["ToastProvider.useCallback[updateToast]"])
            }["ToastProvider.useCallback[updateToast]"]);
        }
    }["ToastProvider.useCallback[updateToast]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToastContext.Provider, {
        value: {
            toasts,
            addToast,
            removeToast,
            updateToast
        },
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToastContainer, {}, void 0, false, {
                fileName: "[project]/providers/ToastProvider.tsx",
                lineNumber: 58,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/providers/ToastProvider.tsx",
        lineNumber: 56,
        columnNumber: 9
    }, this);
};
_s1(ToastProvider, "Vd/D5a7GqOm62072qGdb9CzTY/w=");
_c = ToastProvider;
const ToastContainer = ()=>{
    _s2();
    const { toasts, removeToast } = useToast();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed top-4 right-4 z-200 flex flex-col gap-2 p-4 max-h-screen overflow-hidden pointer-events-none",
        "aria-live": "polite",
        role: "region",
        "aria-label": "Notifications",
        children: toasts.map((toast)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToastItem, {
                toast: toast,
                onClose: ()=>removeToast(toast.id)
            }, toast.id, false, {
                fileName: "[project]/providers/ToastProvider.tsx",
                lineNumber: 74,
                columnNumber: 17
            }, this))
    }, void 0, false, {
        fileName: "[project]/providers/ToastProvider.tsx",
        lineNumber: 67,
        columnNumber: 9
    }, this);
};
_s2(ToastContainer, "hDKWezg0iwBHWd7k0YqUAkfEZE4=", false, function() {
    return [
        useToast
    ];
});
_c1 = ToastContainer;
const ToastItem = ({ toast, onClose })=>{
    _s3();
    const { id, title, description, type = "info", duration = 8000, action } = toast;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToastItem.useEffect": ()=>{
            if (duration === Number.POSITIVE_INFINITY) return;
            const timer = setTimeout({
                "ToastItem.useEffect.timer": ()=>{
                    onClose();
                }
            }["ToastItem.useEffect.timer"], duration);
            return ({
                "ToastItem.useEffect": ()=>clearTimeout(timer)
            })["ToastItem.useEffect"];
        }
    }["ToastItem.useEffect"], [
        duration,
        onClose
    ]);
    const getTypeStyles = ()=>{
        switch(type){
            case "success":
                return "bg-green-100 border-green-500 text-green-800 dark:bg-green-900/50 dark:border-green-600 dark:text-green-100";
            case "error":
                return "bg-red-100 border-red-500 text-red-800 dark:bg-red-900/50 dark:border-red-600 dark:text-red-100";
            case "warning":
                return "bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/50 dark:border-yellow-600 dark:text-yellow-100";
            case "info":
            default:
                return "bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/50 dark:border-blue-600 dark:text-blue-100";
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("animate-slide-in pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border p-4 shadow-md transition-all", getTypeStyles()),
        role: "alert",
        "aria-labelledby": `toast-${id}-title`,
        "data-state": "open",
        "data-toast-type": type,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-start justify-between",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1",
                    children: [
                        title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            id: `toast-${id}-title`,
                            className: "font-medium",
                            children: title
                        }, void 0, false, {
                            fileName: "[project]/providers/ToastProvider.tsx",
                            lineNumber: 121,
                            columnNumber: 25
                        }, this),
                        description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-1 text-sm opacity-90",
                            children: description
                        }, void 0, false, {
                            fileName: "[project]/providers/ToastProvider.tsx",
                            lineNumber: 125,
                            columnNumber: 37
                        }, this),
                        action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2",
                            children: action
                        }, void 0, false, {
                            fileName: "[project]/providers/ToastProvider.tsx",
                            lineNumber: 126,
                            columnNumber: 32
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/providers/ToastProvider.tsx",
                    lineNumber: 119,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: onClose,
                    className: "ml-4 inline-flex h-6 w-6 shrink-0 items-center justify-center rounded-md text-current opacity-50 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-current",
                    "aria-label": "Close notification",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                        className: "h-4 w-4"
                    }, void 0, false, {
                        fileName: "[project]/providers/ToastProvider.tsx",
                        lineNumber: 133,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/providers/ToastProvider.tsx",
                    lineNumber: 128,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/providers/ToastProvider.tsx",
            lineNumber: 118,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/providers/ToastProvider.tsx",
        lineNumber: 108,
        columnNumber: 9
    }, this);
};
_s3(ToastItem, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c2 = ToastItem;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "ToastProvider");
__turbopack_context__.k.register(_c1, "ToastContainer");
__turbopack_context__.k.register(_c2, "ToastItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "API_URL": (()=>API_URL),
    "fetchWithAuth": (()=>fetchWithAuth),
    "fetchWithAuthFormData": (()=>fetchWithAuthFormData),
    "getAuthHeader": (()=>getAuthHeader),
    "getAuthToken": (()=>getAuthToken),
    "getCurrentUser": (()=>getCurrentUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-client] (ecmascript)");
"use client";
;
const API_URL = ("TURBOPACK compile-time value", "http://localhost:5000") || "http://localhost:5000";
async function getAuthToken() {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSession"])();
    return session?.user?.accessToken;
}
async function getAuthHeader() {
    const token = await getAuthToken();
    return {
        "Content-Type": "application/json",
        ...token ? {
            Authorization: `Bearer ${token}`
        } : {}
    };
}
async function fetchWithAuth(url, options = {}) {
    const headers = await getAuthHeader();
    const response = await fetch(url, {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    });
    // Xử lý lỗi xác thực
    if (response.status === 401) {
        // Đăng xuất người dùng khi token không hợp lệ hoặc hết hạn
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["signOut"])({
            redirect: true,
            callbackUrl: "/login"
        });
        throw new Error("Session expired. Please login again.");
    }
    return response;
}
async function fetchWithAuthFormData(url, formData, method = "POST") {
    const token = await getAuthToken();
    const headers = {};
    if (token) {
        headers["Authorization"] = `Bearer ${token}`;
    }
    const response = await fetch(url, {
        method,
        headers,
        body: formData
    });
    // Xử lý lỗi xác thực
    if (response.status === 401) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["signOut"])({
            redirect: true,
            callbackUrl: "/login"
        });
        throw new Error("Session expired. Please login again.");
    }
    return response;
}
async function getCurrentUser() {
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSession"])();
    return session?.user || null;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/query-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ /**
 * Utility functions for building API query parameters
 */ /**
 * Builds query string from filter parameters
 * @param params - Object containing filter parameters
 * @returns URLSearchParams object
 */ __turbopack_context__.s({
    "buildApiUrl": (()=>buildApiUrl),
    "buildQueryParams": (()=>buildQueryParams),
    "cleanFilters": (()=>cleanFilters),
    "createDefaultPagination": (()=>createDefaultPagination),
    "debounce": (()=>debounce),
    "formatDateForApi": (()=>formatDateForApi),
    "getSelectValue": (()=>getSelectValue),
    "hasActiveFilters": (()=>hasActiveFilters),
    "parseDateFromApi": (()=>parseDateFromApi),
    "parseSelectValue": (()=>parseSelectValue),
    "shouldResetPagination": (()=>shouldResetPagination),
    "validatePaginationParams": (()=>validatePaginationParams)
});
function buildQueryParams(params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== '') {
            // Handle boolean values
            if (typeof value === 'boolean') {
                searchParams.append(key, value.toString());
            } else if (typeof value === 'number') {
                searchParams.append(key, value.toString());
            } else if (typeof value === 'string') {
                searchParams.append(key, value);
            } else if (value instanceof Date) {
                searchParams.append(key, value.toISOString());
            }
        }
    });
    return searchParams;
}
function buildApiUrl(baseUrl, params) {
    const queryParams = buildQueryParams(params);
    const queryString = queryParams.toString();
    if (queryString) {
        const separator = baseUrl.includes('?') ? '&' : '?';
        return `${baseUrl}${separator}${queryString}`;
    }
    return baseUrl;
}
function cleanFilters(obj) {
    const cleaned = {};
    Object.entries(obj).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== '') {
            cleaned[key] = value;
        }
    });
    return cleaned;
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function hasActiveFilters(filters) {
    return Object.values(filters).some((value)=>value !== undefined && value !== null && value !== '' && !(Array.isArray(value) && value.length === 0));
}
function shouldResetPagination(filters, previousFilters) {
    // Compare all filter values except pagination-related ones
    const filterKeys = Object.keys(filters).filter((key)=>![
            'pageNumber',
            'pageSize',
            'skip'
        ].includes(key));
    return filterKeys.some((key)=>filters[key] !== previousFilters[key]);
}
function formatDateForApi(date) {
    if (!date) return undefined;
    if (typeof date === 'string') {
        return new Date(date).toISOString();
    }
    return date.toISOString();
}
function parseDateFromApi(dateString) {
    if (!dateString) return undefined;
    return new Date(dateString);
}
function createDefaultPagination(pageSize = 25) {
    return {
        pageNumber: 1,
        pageSize,
        sortBy: 'createdAt',
        sortDirection: 'desc',
        isDescending: true
    };
}
function validatePaginationParams(params) {
    return {
        pageNumber: Math.max(1, params.pageNumber || 1),
        pageSize: Math.min(100, Math.max(1, params.pageSize || 25)),
        sortBy: params.sortBy || 'createdAt',
        sortDirection: params.sortDirection || 'desc',
        isDescending: params.isDescending ?? true
    };
}
function getSelectValue(value, defaultValue = "all") {
    if (value === undefined || value === null || value === "") {
        return defaultValue;
    }
    return value.toString();
}
function parseSelectValue(value, defaultValue = "all", type = 'string') {
    if (value === defaultValue) {
        return undefined;
    }
    switch(type){
        case 'number':
            return parseInt(value);
        case 'float':
            return parseFloat(value);
        case 'string':
        default:
            return value;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/categories.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createCategory": (()=>createCategory),
    "deleteCategory": (()=>deleteCategory),
    "getAllCategories": (()=>getAllCategories),
    "getCategories": (()=>getCategories),
    "getCategoriesWithProductCount": (()=>getCategoriesWithProductCount),
    "getCategoryById": (()=>getCategoryById),
    "getCategoryBySlug": (()=>getCategoryBySlug),
    "getCategoryProductCount": (()=>getCategoryProductCount),
    "getHierarchicalCategories": (()=>getHierarchicalCategories),
    "getPopularCategories": (()=>getPopularCategories),
    "getProductsByCategory": (()=>getProductsByCategory),
    "getRootCategories": (()=>getRootCategories),
    "getSubcategories": (()=>getSubcategories),
    "updateCategory": (()=>updateCategory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/query-utils.ts [app-client] (ecmascript)");
"use client";
;
;
async function getCategories(filters) {
    try {
        const cleanedFilters = filters ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanFilters"])(filters) : {};
        const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildApiUrl"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category`, cleanedFilters);
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(url);
        if (!response.ok) {
            throw new Error("Unable to fetch categories");
        }
        return response.json();
    } catch (error) {
        console.error("Get categories error:", error);
        throw new Error("Unable to fetch categories. Please try again later.");
    }
}
async function getAllCategories() {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/all`);
        if (!response.ok) {
            throw new Error("Unable to fetch categories");
        }
        return response.json();
    } catch (error) {
        console.error("Get all categories error:", error);
        throw new Error("Unable to fetch categories. Please try again later.");
    }
}
async function getHierarchicalCategories() {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/hierarchical`);
        if (!response.ok) {
            throw new Error("Unable to fetch hierarchical categories");
        }
        return response.json();
    } catch (error) {
        console.error("Get hierarchical categories error:", error);
        throw new Error("Unable to fetch hierarchical categories. Please try again later.");
    }
}
async function getCategoryById(id) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/${id}`);
        if (!response.ok) {
            throw new Error("Unable to fetch category");
        }
        return response.json();
    } catch (error) {
        console.error(`Get category by id ${id} error:`, error);
        throw new Error("Unable to fetch category. Please try again later.");
    }
}
async function createCategory(category) {
    try {
        const categoryData = {
            name: category.name,
            slug: category.slug,
            description: category.description,
            parentId: category.parentId,
            isActive: category.isActive ?? true,
            displayOrder: category.displayOrder ?? 0,
            imageId: category.imageId
        };
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(categoryData)
        });
        if (!response.ok) {
            throw new Error("Unable to create category");
        }
        return response.json();
    } catch (error) {
        console.error("Create category error:", error);
        throw new Error("Unable to create category. Please try again later.");
    }
}
async function updateCategory(category) {
    try {
        const categoryData = {};
        if (category.name) {
            categoryData.name = category.name;
        }
        if (category.slug) {
            categoryData.slug = category.slug;
        }
        if (category.description !== undefined) {
            categoryData.description = category.description;
        }
        if (category.parentId !== undefined) {
            categoryData.parentId = category.parentId;
        }
        if (category.isActive !== undefined) {
            categoryData.isActive = category.isActive;
        }
        if (category.displayOrder !== undefined) {
            categoryData.displayOrder = category.displayOrder;
        }
        if (category.imageId !== undefined) {
            categoryData.imageId = category.imageId;
        }
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/${category.id}`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(categoryData)
        });
        if (!response.ok) {
            throw new Error("Unable to update category");
        }
    } catch (error) {
        console.error(`Update category error:`, error);
        throw new Error("Unable to update category. Please try again later.");
    }
}
async function deleteCategory(id) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/${id}`, {
            method: "DELETE"
        });
        if (!response.ok) {
            throw new Error("Unable to delete category");
        }
    } catch (error) {
        console.error(`Delete category by id ${id} error:`, error);
        throw new Error("Unable to delete category. Please try again later.");
    }
}
async function getProductsByCategory(categoryId) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/${categoryId}/Products`);
        if (!response.ok) {
            throw new Error("Unable to fetch products for this category");
        }
        return response.json();
    } catch (error) {
        console.error(`Get products by category ${categoryId} error:`, error);
        throw new Error("Unable to fetch products for this category. Please try again later.");
    }
}
async function getCategoryBySlug(slug) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/slug/${encodeURIComponent(slug)}`);
        if (!response.ok) {
            throw new Error("Unable to fetch category");
        }
        return response.json();
    } catch (error) {
        console.error(`Get category by slug ${slug} error:`, error);
        throw new Error("Unable to fetch category. Please try again later.");
    }
}
async function getCategoriesWithProductCount() {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/with-product-count`);
        if (!response.ok) {
            throw new Error("Unable to fetch categories with product count");
        }
        return response.json();
    } catch (error) {
        console.error("Get categories with product count error:", error);
        throw new Error("Unable to fetch categories with product count. Please try again later.");
    }
}
async function getSubcategories(parentId) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/${parentId}/subcategories`);
        if (!response.ok) {
            throw new Error("Unable to fetch subcategories");
        }
        return response.json();
    } catch (error) {
        console.error(`Get subcategories for parent ${parentId} error:`, error);
        throw new Error("Unable to fetch subcategories. Please try again later.");
    }
}
async function getCategoryProductCount(categoryId) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/${categoryId}/product-count`);
        if (!response.ok) {
            throw new Error("Unable to fetch category product count");
        }
        return response.json();
    } catch (error) {
        console.error(`Get category product count for ${categoryId} error:`, error);
        throw new Error("Unable to fetch category product count. Please try again later.");
    }
}
async function getPopularCategories() {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/popular`);
        if (!response.ok) {
            throw new Error("Unable to fetch popular categories");
        }
        return response.json();
    } catch (error) {
        console.error("Get popular categories error:", error);
        throw new Error("Unable to fetch popular categories. Please try again later.");
    }
}
async function getRootCategories() {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/Category/root`);
        if (!response.ok) {
            throw new Error("Unable to fetch root categories");
        }
        return response.json();
    } catch (error) {
        console.error("Get root categories error:", error);
        throw new Error("Unable to fetch root categories. Please try again later.");
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/stores/categoryStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCategoryStore": (()=>useCategoryStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$categories$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/categories.ts [app-client] (ecmascript)");
"use client";
;
;
;
/**
 * Utility function to flatten hierarchical categories
 */ const flattenCategories = (categories)=>{
    const flattened = [];
    const flatten = (cats)=>{
        cats.forEach((category)=>{
            flattened.push(category);
            if (category.subcategories && category.subcategories.length > 0) {
                flatten(category.subcategories);
            }
        });
    };
    flatten(categories);
    return flattened;
};
/**
 * Utility function to find category by ID in hierarchical structure
 */ const findCategoryById = (categories, id)=>{
    for (const category of categories){
        if (category.id === id) {
            return category;
        }
        if (category.subcategories && category.subcategories.length > 0) {
            const found = findCategoryById(category.subcategories, id);
            if (found) return found;
        }
    }
    return undefined;
};
/**
 * Utility function to update category in hierarchical structure
 */ const updateCategoryInHierarchy = (categories, updatedCategory)=>{
    return categories.map((category)=>{
        if (category.id === updatedCategory.id) {
            return {
                ...updatedCategory,
                subcategories: category.subcategories
            };
        }
        if (category.subcategories && category.subcategories.length > 0) {
            return {
                ...category,
                subcategories: updateCategoryInHierarchy(category.subcategories, updatedCategory)
            };
        }
        return category;
    });
};
/**
 * Utility function to remove category from hierarchical structure
 */ const removeCategoryFromHierarchy = (categories, categoryId)=>{
    return categories.filter((category)=>category.id !== categoryId).map((category)=>({
            ...category,
            subcategories: category.subcategories ? removeCategoryFromHierarchy(category.subcategories, categoryId) : undefined
        }));
};
/**
 * Utility function to add category to hierarchical structure
 */ const addCategoryToHierarchy = (categories, newCategory)=>{
    // If it's a root category (no parentId)
    if (!newCategory.parentId) {
        return [
            ...categories,
            newCategory
        ];
    }
    // Find parent and add as subcategory
    return categories.map((category)=>{
        if (category.id === newCategory.parentId) {
            return {
                ...category,
                subcategories: [
                    ...category.subcategories || [],
                    newCategory
                ]
            };
        }
        if (category.subcategories && category.subcategories.length > 0) {
            return {
                ...category,
                subcategories: addCategoryToHierarchy(category.subcategories, newCategory)
            };
        }
        return category;
    });
};
const useCategoryStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])((set, get)=>({
        // Initial state
        categories: [],
        flatCategories: [],
        isLoading: false,
        isInitialized: false,
        error: null,
        // Initialize categories from API
        initializeCategories: async ()=>{
            const { isInitialized, isLoading } = get();
            // Prevent multiple initializations
            if (isInitialized || isLoading) {
                return;
            }
            set({
                isLoading: true,
                error: null
            });
            try {
                const hierarchicalCategories = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$categories$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHierarchicalCategories"])();
                const flatCategories = flattenCategories(hierarchicalCategories);
                set({
                    categories: hierarchicalCategories,
                    flatCategories,
                    isLoading: false,
                    isInitialized: true,
                    error: null
                });
            } catch (error) {
                console.error("Failed to initialize categories:", error);
                set({
                    isLoading: false,
                    error: error instanceof Error ? error.message : "Failed to load categories"
                });
            }
        },
        // Add new category
        addCategory: (category)=>{
            const { categories } = get();
            const updatedCategories = addCategoryToHierarchy(categories, category);
            const flatCategories = flattenCategories(updatedCategories);
            set({
                categories: updatedCategories,
                flatCategories
            });
        },
        // Update existing category
        updateCategory: (category)=>{
            const { categories } = get();
            const updatedCategories = updateCategoryInHierarchy(categories, category);
            const flatCategories = flattenCategories(updatedCategories);
            set({
                categories: updatedCategories,
                flatCategories
            });
        },
        // Remove category
        removeCategory: (categoryId)=>{
            const { categories } = get();
            const updatedCategories = removeCategoryFromHierarchy(categories, categoryId);
            const flatCategories = flattenCategories(updatedCategories);
            set({
                categories: updatedCategories,
                flatCategories
            });
        },
        // Clear error
        clearError: ()=>{
            set({
                error: null
            });
        },
        // Reset store
        reset: ()=>{
            set({
                categories: [],
                flatCategories: [],
                isLoading: false,
                isInitialized: false,
                error: null
            });
        },
        // Selectors
        getCategoryById: (id)=>{
            const { flatCategories } = get();
            return flatCategories.find((category)=>category.id === id);
        },
        getRootCategories: ()=>{
            const { categories } = get();
            return categories;
        },
        getSubcategories: (parentId)=>{
            const { categories } = get();
            const parent = findCategoryById(categories, parentId);
            return parent?.subcategories || [];
        },
        getCategoryPath: (categoryId)=>{
            const { flatCategories } = get();
            const path = [];
            const findPath = (id)=>{
                const category = flatCategories.find((cat)=>cat.id === id);
                if (!category) return false;
                path.unshift(category);
                if (category.parentId) {
                    return findPath(category.parentId);
                }
                return true;
            };
            findPath(categoryId);
            return path;
        }
    }), {
    name: "category-store"
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/providers/CategoryStoreProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryStoreProvider": (()=>CategoryStoreProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$categoryStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/stores/categoryStore.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
function CategoryStoreProvider({ children }) {
    _s();
    const { data: session, status } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSession"])();
    const { initializeCategories, reset, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$categoryStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCategoryStore"])();
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Ensure this only runs on client side to avoid hydration mismatch
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CategoryStoreProvider.useEffect": ()=>{
            setIsClient(true);
        }
    }["CategoryStoreProvider.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CategoryStoreProvider.useEffect": ()=>{
            // Only run on client side
            if (!isClient) return;
            // Initialize categories when user is authenticated
            if (status === "authenticated" && session?.user && !isInitialized) {
                console.log("Initializing category store for authenticated user");
                initializeCategories();
            }
            // Reset store when user logs out
            if (status === "unauthenticated") {
                console.log("Resetting category store for unauthenticated user");
                reset();
            }
        }
    }["CategoryStoreProvider.useEffect"], [
        isClient,
        status,
        session,
        isInitialized,
        initializeCategories,
        reset
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(CategoryStoreProvider, "Oxfe6cMNMTISVe4ktoty3U2R18w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSession"],
        __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$categoryStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCategoryStore"]
    ];
});
_c = CategoryStoreProvider;
var _c;
__turbopack_context__.k.register(_c, "CategoryStoreProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/fileManager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * File Manager API Service
 */ __turbopack_context__.s({
    "fileManagerService": (()=>fileManagerService),
    "fileManagerUtils": (()=>fileManagerUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api-utils.ts [app-client] (ecmascript)");
;
const BASE_URL = `${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_URL"]}/api/filemanager`;
const fileManagerService = {
    /**
   * Health check
   */ async healthCheck () {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/health`);
        if (!response.ok) {
            throw new Error("Failed to check health");
        }
        return response.json();
    },
    /**
   * Browse files and folders
   */ async browse (params = {}) {
        const url = `${BASE_URL}/browse`;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                path: params.path || "",
                search: params.search || "",
                fileType: params.fileType || "all",
                extension: params.extension || "",
                sortBy: params.sortBy || "name",
                sortOrder: params.sortOrder || "asc",
                minSize: params.minSize,
                maxSize: params.maxSize,
                fromDate: params.fromDate,
                toDate: params.toDate,
                pageNumber: params.page || 1,
                pageSize: params.pageSize || 20
            })
        });
        if (!response.ok) throw new Error("Failed to browse files");
        return response.json();
    },
    /**
   * Get folder structure
   */ async getFolderStructure (rootPath = "") {
        const searchParams = new URLSearchParams();
        if (rootPath) searchParams.append("rootPath", rootPath);
        const url = `${BASE_URL}/folder-structure${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(url);
        if (!response.ok) {
            throw new Error("Failed to get folder structure");
        }
        return response.json();
    },
    /**
   * Get file info
   */ async getFileInfo (filePath) {
        const searchParams = new URLSearchParams();
        searchParams.append("filePath", filePath);
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/info?${searchParams.toString()}`);
        if (!response.ok) {
            throw new Error("Failed to get file info");
        }
        return response.json();
    },
    /**
   * Upload files
   */ async uploadFiles (files, folderPath = "", createThumbnails = true, overwriteExisting = false) {
        const formData = new FormData();
        files.forEach((file)=>{
            formData.append("files", file);
        });
        formData.append("FolderPath", folderPath);
        formData.append("CreateThumbnails", createThumbnails.toString());
        formData.append("OverwriteExisting", overwriteExisting.toString());
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuthFormData"])(`${BASE_URL}/upload`, formData, "POST");
        if (!response.ok) {
            throw new Error("Failed to upload files");
        }
        return response.json();
    },
    /**
   * Create folder
   */ async createFolder (request) {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/create-folder`, {
            method: "POST",
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            throw new Error("Failed to create folder");
        }
        return response.json();
    },
    /**
   * Delete files/folders
   */ async deleteFiles (request) {
        console.log("Deleting files with request:", request);
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/delete`, {
            method: "DELETE",
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            throw new Error("Failed to delete files");
        }
        return response.json();
    },
    /**
   * Move files
   */ async moveFiles (request) {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/move`, {
            method: "POST",
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            throw new Error("Failed to move files");
        }
        return response.json();
    },
    /**
   * Copy files
   */ async copyFiles (request) {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/copy`, {
            method: "POST",
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            throw new Error("Failed to copy files");
        }
        return response.json();
    },
    /**
   * Generate thumbnail
   */ async generateThumbnail (imagePath) {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/generate-thumbnail`, {
            method: "POST",
            body: JSON.stringify(imagePath)
        });
        if (!response.ok) {
            throw new Error("Failed to generate thumbnail");
        }
        return response.json();
    },
    /**
   * Get image metadata
   */ async getImageMetadata (imagePath) {
        const searchParams = new URLSearchParams();
        searchParams.append("imagePath", imagePath);
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/metadata?${searchParams.toString()}`);
        if (!response.ok) {
            throw new Error("Failed to get image metadata");
        }
        return response.json();
    },
    /**
   * Cleanup orphaned files
   */ async cleanupOrphanedFiles () {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/cleanup-orphaned`, {
            method: "POST"
        });
        if (!response.ok) {
            throw new Error("Failed to cleanup orphaned files");
        }
        return response.json();
    },
    /**
   * Sync database
   */ async syncDatabase () {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/sync-database`, {
            method: "POST"
        });
        if (!response.ok) {
            throw new Error("Failed to sync database");
        }
        return response.json();
    },
    /**
   * Get missing files
   */ async getMissingFiles () {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(`${BASE_URL}/missing-files`);
        if (!response.ok) {
            throw new Error("Failed to get missing files");
        }
        return response.json();
    },
    /**
   * Get download URL for a file
   */ getDownloadUrl (relativePath) {
        // API_URL is imported from @/lib/api-utils
        // API endpoint uses filePath parameter as per documentation
        return `${BASE_URL}/download?filePath=${encodeURIComponent(relativePath)}`;
    }
};
const fileManagerUtils = {
    /**
   * Get file icon based on extension
   */ getFileIcon (extension) {
        const ext = extension.toLowerCase();
        if ([
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".webp",
            ".bmp"
        ].includes(ext)) {
            return "image";
        }
        if ([
            ".pdf"
        ].includes(ext)) {
            return "file-text";
        }
        if ([
            ".doc",
            ".docx"
        ].includes(ext)) {
            return "file-text";
        }
        if ([
            ".xls",
            ".xlsx"
        ].includes(ext)) {
            return "file-spreadsheet";
        }
        if ([
            ".ppt",
            ".pptx"
        ].includes(ext)) {
            return "presentation";
        }
        if ([
            ".mp4",
            ".avi",
            ".mov",
            ".wmv"
        ].includes(ext)) {
            return "video";
        }
        if ([
            ".mp3",
            ".wav",
            ".flac",
            ".aac"
        ].includes(ext)) {
            return "music";
        }
        if ([
            ".zip",
            ".rar",
            ".7z"
        ].includes(ext)) {
            return "archive";
        }
        return "file";
    },
    /**
   * Format file size
   */ formatFileSize (bytes) {
        if (bytes === 0) return "0 B";
        const k = 1024;
        const sizes = [
            "B",
            "KB",
            "MB",
            "GB",
            "TB"
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
    /**
   * Get breadcrumb from path
   */ getBreadcrumb (path) {
        if (!path) return [
            {
                name: "My Drive",
                path: ""
            }
        ];
        const parts = path.split("/").filter(Boolean);
        const breadcrumb = [
            {
                name: "My Drive",
                path: ""
            }
        ];
        let currentPath = "";
        parts.forEach((part)=>{
            currentPath += (currentPath ? "/" : "") + part;
            breadcrumb.push({
                name: part,
                path: currentPath
            });
        });
        return breadcrumb;
    },
    /**
   * Check if file is image
   */ isImage (extension) {
        const imageExtensions = [
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".webp",
            ".bmp"
        ];
        return imageExtensions.includes(extension.toLowerCase());
    },
    /**
   * Get file type color
   */ getFileTypeColor (type, extension) {
        if (type === "folder") return "text-blue-500";
        if (type === "image") return "text-green-500";
        const ext = extension.toLowerCase();
        if ([
            ".pdf"
        ].includes(ext)) return "text-red-500";
        if ([
            ".doc",
            ".docx"
        ].includes(ext)) return "text-blue-600";
        if ([
            ".xls",
            ".xlsx"
        ].includes(ext)) return "text-green-600";
        if ([
            ".ppt",
            ".pptx"
        ].includes(ext)) return "text-orange-500";
        if ([
            ".mp4",
            ".avi",
            ".mov"
        ].includes(ext)) return "text-purple-500";
        if ([
            ".mp3",
            ".wav",
            ".flac"
        ].includes(ext)) return "text-pink-500";
        return "text-gray-500";
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/use-toast.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "reducer": (()=>reducer),
    "useToast": (()=>useToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$ToastProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/providers/ToastProvider.tsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 1000000;
const actionTypes = {
    ADD_TOAST: "ADD_TOAST",
    UPDATE_TOAST: "UPDATE_TOAST",
    DISMISS_TOAST: "DISMISS_TOAST",
    REMOVE_TOAST: "REMOVE_TOAST"
};
let count = 0;
function genId() {
    count = (count + 1) % Number.MAX_SAFE_INTEGER;
    return count.toString();
}
const toastTimeouts = new Map();
const addToRemoveQueue = (toastId)=>{
    if (toastTimeouts.has(toastId)) {
        return;
    }
    const timeout = setTimeout(()=>{
        toastTimeouts.delete(toastId);
        dispatch({
            type: "REMOVE_TOAST",
            toastId: toastId
        });
    }, TOAST_REMOVE_DELAY);
    toastTimeouts.set(toastId, timeout);
};
const reducer = (state, action)=>{
    switch(action.type){
        case "ADD_TOAST":
            return {
                ...state,
                toasts: [
                    action.toast,
                    ...state.toasts
                ].slice(0, TOAST_LIMIT)
            };
        case "UPDATE_TOAST":
            return {
                ...state,
                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {
                        ...t,
                        ...action.toast
                    } : t)
            };
        case "DISMISS_TOAST":
            {
                const { toastId } = action;
                // ! Side effects ! - This could be extracted into a dismissToast() action,
                // but I'll keep it here for simplicity
                if (toastId) {
                    addToRemoveQueue(toastId);
                } else {
                    state.toasts.forEach((toast)=>{
                        addToRemoveQueue(toast.id);
                    });
                }
                return {
                    ...state,
                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {
                            ...t,
                            open: false
                        } : t)
                };
            }
        case "REMOVE_TOAST":
            if (action.toastId === undefined) {
                return {
                    ...state,
                    toasts: []
                };
            }
            return {
                ...state,
                toasts: state.toasts.filter((t)=>t.id !== action.toastId)
            };
    }
};
const listeners = [];
let memoryState = {
    toasts: []
};
function dispatch(action) {
    memoryState = reducer(memoryState, action);
    listeners.forEach((listener)=>{
        listener(memoryState);
    });
}
function useToast() {
    _s();
    const { addToast, removeToast, updateToast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$ToastProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    return {
        success: (props)=>addToast({
                ...props,
                type: "success"
            }),
        error: (props)=>addToast({
                ...props,
                type: "error"
            }),
        warning: (props)=>addToast({
                ...props,
                type: "warning"
            }),
        info: (props)=>addToast({
                ...props,
                type: "info"
            }),
        custom: (props)=>addToast(props),
        dismiss: (id)=>removeToast(id),
        update: (id, props)=>updateToast(id, props),
        promise: async (promise, options)=>{
            const toastId = addToast({
                ...options.loading,
                type: "info"
            });
            try {
                const data = await promise;
                updateToast(toastId, {
                    ...options.success(data),
                    type: "success"
                });
                return data;
            } catch (err) {
                updateToast(toastId, {
                    ...options.error(err),
                    type: "error"
                });
                throw err;
            }
        }
    };
}
_s(useToast, "LiBJpSyKO4ZeYCCvDcDyRm/Nztw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$ToastProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/contexts/FileManagerContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * File Manager Context
 */ __turbopack_context__.s({
    "FileManagerProvider": (()=>FileManagerProvider),
    "fileManagerKeys": (()=>fileManagerKeys),
    "useFileManagerContext": (()=>useFileManagerContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/fileManager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api-utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
const fileManagerKeys = {
    all: [
        "fileManager"
    ],
    browse: (params)=>[
            ...fileManagerKeys.all,
            "browse",
            params
        ],
    folders: (rootPath)=>[
            ...fileManagerKeys.all,
            "folders",
            rootPath
        ],
    fileInfo: (filePath)=>[
            ...fileManagerKeys.all,
            "fileInfo",
            filePath
        ]
};
// Default filters
const defaultFilters = {
    search: "",
    fileType: "all",
    extension: "",
    sortBy: "name",
    sortOrder: "asc",
    dateRange: {},
    sizeRange: {}
};
// Create context
const FileManagerContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const FileManagerProvider = ({ children })=>{
    _s();
    const toast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    // State
    const [currentPath, setCurrentPath] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [selectedItems, setSelectedItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [viewMode, setViewMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("grid");
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultFilters);
    const [uploadProgress, setUploadProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Browse files query
    const browseParams = {
        path: currentPath,
        page: 1,
        pageSize: 20,
        search: filters.search || undefined,
        fileType: filters.fileType !== "all" ? filters.fileType : undefined,
        extension: filters.extension || undefined,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        minSize: filters.sizeRange.min,
        maxSize: filters.sizeRange.max,
        fromDate: filters.dateRange.from,
        toDate: filters.dateRange.to
    };
    const { data: browseData, isLoading: isBrowseLoading, error: browseError, refetch: refetchBrowse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: fileManagerKeys.browse(browseParams),
        queryFn: {
            "FileManagerProvider.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].browse(browseParams)
        }["FileManagerProvider.useQuery"],
        staleTime: 30000
    });
    // Folder structure query
    const { data: folderStructure, isLoading: isFoldersLoading, refetch: refetchFolders } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: fileManagerKeys.folders(currentPath),
        queryFn: {
            "FileManagerProvider.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].getFolderStructure(currentPath)
        }["FileManagerProvider.useQuery"],
        staleTime: 60000
    });
    // Root folder structure query
    const { data: rootFolderStructure, isLoading: isRootFoldersLoading, refetch: refetchRootFolders } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: fileManagerKeys.folders(""),
        queryFn: {
            "FileManagerProvider.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].getFolderStructure("")
        }["FileManagerProvider.useQuery"],
        staleTime: 60000
    });
    // Upload mutation
    const uploadMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "FileManagerProvider.useMutation[uploadMutation]": async ({ files, folderPath, createThumbnails = true, overwriteExisting = false })=>{
                // Initialize upload progress
                const initialProgress = files.map({
                    "FileManagerProvider.useMutation[uploadMutation].initialProgress": (file)=>({
                            fileName: file.name,
                            progress: 0,
                            status: "pending"
                        })
                }["FileManagerProvider.useMutation[uploadMutation].initialProgress"]);
                setUploadProgress(initialProgress);
                // Simulate progress updates (in real app, this would come from upload progress)
                let progressStep = 0;
                const progressInterval = setInterval({
                    "FileManagerProvider.useMutation[uploadMutation].progressInterval": ()=>{
                        progressStep += 1;
                        setUploadProgress({
                            "FileManagerProvider.useMutation[uploadMutation].progressInterval": (prev)=>prev.map({
                                    "FileManagerProvider.useMutation[uploadMutation].progressInterval": (item)=>({
                                            ...item,
                                            progress: Math.min(item.progress + 15 + progressStep % 3 * 5, 90),
                                            status: item.progress < 90 ? "uploading" : item.status
                                        })
                                }["FileManagerProvider.useMutation[uploadMutation].progressInterval"])
                        }["FileManagerProvider.useMutation[uploadMutation].progressInterval"]);
                    }
                }["FileManagerProvider.useMutation[uploadMutation].progressInterval"], 500);
                try {
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].uploadFiles(files, folderPath || currentPath, createThumbnails, overwriteExisting);
                    clearInterval(progressInterval);
                    // Mark as completed
                    setUploadProgress({
                        "FileManagerProvider.useMutation[uploadMutation]": (prev)=>prev.map({
                                "FileManagerProvider.useMutation[uploadMutation]": (item)=>({
                                        ...item,
                                        progress: 100,
                                        status: "completed"
                                    })
                            }["FileManagerProvider.useMutation[uploadMutation]"])
                    }["FileManagerProvider.useMutation[uploadMutation]"]);
                    return result;
                } catch (error) {
                    clearInterval(progressInterval);
                    // Mark as error
                    setUploadProgress({
                        "FileManagerProvider.useMutation[uploadMutation]": (prev)=>prev.map({
                                "FileManagerProvider.useMutation[uploadMutation]": (item)=>({
                                        ...item,
                                        status: "error",
                                        error: error instanceof Error ? error.message : "Upload failed"
                                    })
                            }["FileManagerProvider.useMutation[uploadMutation]"])
                    }["FileManagerProvider.useMutation[uploadMutation]"]);
                    throw error;
                }
            }
        }["FileManagerProvider.useMutation[uploadMutation]"],
        onSuccess: {
            "FileManagerProvider.useMutation[uploadMutation]": (data)=>{
                toast.success({
                    title: "Upload successful",
                    description: `${data.successCount} files uploaded successfully`
                });
                // Invalidate queries
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.browse(browseParams)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders(currentPath)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders("")
                }); // Refetch root folders
                // Clear upload progress after delay
                setTimeout({
                    "FileManagerProvider.useMutation[uploadMutation]": ()=>setUploadProgress([])
                }["FileManagerProvider.useMutation[uploadMutation]"], 3000);
            }
        }["FileManagerProvider.useMutation[uploadMutation]"],
        onError: {
            "FileManagerProvider.useMutation[uploadMutation]": (error)=>{
                toast.error({
                    title: "Upload failed",
                    description: error instanceof Error ? error.message : "Failed to upload files"
                });
            }
        }["FileManagerProvider.useMutation[uploadMutation]"]
    });
    // Create folder mutation
    const createFolderMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "FileManagerProvider.useMutation[createFolderMutation]": (request)=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].createFolder(request)
        }["FileManagerProvider.useMutation[createFolderMutation]"],
        onSuccess: {
            "FileManagerProvider.useMutation[createFolderMutation]": (data)=>{
                toast.success({
                    title: "Folder created",
                    description: `Folder "${data.name}" created successfully`
                });
                // Invalidate queries
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.browse(browseParams)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders(currentPath)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders("")
                }); // Refetch root folders
            }
        }["FileManagerProvider.useMutation[createFolderMutation]"],
        onError: {
            "FileManagerProvider.useMutation[createFolderMutation]": (error)=>{
                toast.error({
                    title: "Failed to create folder",
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            }
        }["FileManagerProvider.useMutation[createFolderMutation]"]
    });
    // Delete mutation
    const deleteMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "FileManagerProvider.useMutation[deleteMutation]": (request)=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].deleteFiles(request)
        }["FileManagerProvider.useMutation[deleteMutation]"],
        onSuccess: {
            "FileManagerProvider.useMutation[deleteMutation]": (data)=>{
                toast.success({
                    title: "Files deleted",
                    description: `${data.successCount} items deleted successfully`
                });
                // Clear selection
                setSelectedItems([]);
                // Invalidate queries
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.browse(browseParams)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders(currentPath)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders("")
                }); // Refetch root folders
            }
        }["FileManagerProvider.useMutation[deleteMutation]"],
        onError: {
            "FileManagerProvider.useMutation[deleteMutation]": (error)=>{
                toast.error({
                    title: "Failed to delete files",
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            }
        }["FileManagerProvider.useMutation[deleteMutation]"]
    });
    // Move mutation
    const moveMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "FileManagerProvider.useMutation[moveMutation]": (request)=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].moveFiles(request)
        }["FileManagerProvider.useMutation[moveMutation]"],
        onSuccess: {
            "FileManagerProvider.useMutation[moveMutation]": (data)=>{
                toast.success({
                    title: "Files moved",
                    description: `${data.successCount} items moved successfully`
                });
                // Clear selection
                setSelectedItems([]);
                // Invalidate queries
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.all
                });
            }
        }["FileManagerProvider.useMutation[moveMutation]"],
        onError: {
            "FileManagerProvider.useMutation[moveMutation]": (error)=>{
                toast.error({
                    title: "Failed to move files",
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            }
        }["FileManagerProvider.useMutation[moveMutation]"]
    });
    // Copy mutation
    const copyMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "FileManagerProvider.useMutation[copyMutation]": (request)=>__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].copyFiles(request)
        }["FileManagerProvider.useMutation[copyMutation]"],
        onSuccess: {
            "FileManagerProvider.useMutation[copyMutation]": (data)=>{
                toast.success({
                    title: "Files copied",
                    description: `${data.successCount} items copied successfully`
                });
                // Clear selection
                setSelectedItems([]);
                // Invalidate queries
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.browse(browseParams)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders(currentPath)
                });
                queryClient.invalidateQueries({
                    queryKey: fileManagerKeys.folders("")
                }); // Refetch root folders
            }
        }["FileManagerProvider.useMutation[copyMutation]"],
        onError: {
            "FileManagerProvider.useMutation[copyMutation]": (error)=>{
                toast.error({
                    title: "Failed to copy files",
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            }
        }["FileManagerProvider.useMutation[copyMutation]"]
    });
    // Actions
    const navigateToPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[navigateToPath]": (path)=>{
            setCurrentPath(path);
            setSelectedItems([]);
        }
    }["FileManagerProvider.useCallback[navigateToPath]"], []);
    const navigateUp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[navigateUp]": ()=>{
            if (currentPath) {
                const parentPath = currentPath.split("/").slice(0, -1).join("/");
                navigateToPath(parentPath);
            }
        }
    }["FileManagerProvider.useCallback[navigateUp]"], [
        currentPath,
        navigateToPath
    ]);
    const selectItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[selectItem]": (relativePath)=>{
            setSelectedItems({
                "FileManagerProvider.useCallback[selectItem]": (prevSelected)=>{
                    if (prevSelected.includes(relativePath)) {
                        return prevSelected.filter({
                            "FileManagerProvider.useCallback[selectItem]": (item)=>item !== relativePath
                        }["FileManagerProvider.useCallback[selectItem]"]);
                    }
                    return [
                        ...prevSelected,
                        relativePath
                    ];
                }
            }["FileManagerProvider.useCallback[selectItem]"]);
        }
    }["FileManagerProvider.useCallback[selectItem]"], []);
    const selectAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[selectAll]": ()=>{
            if (browseData?.items) {
                const allPaths = browseData.items.map({
                    "FileManagerProvider.useCallback[selectAll].allPaths": (item)=>item.relativePath
                }["FileManagerProvider.useCallback[selectAll].allPaths"]);
                setSelectedItems(allPaths);
            }
        }
    }["FileManagerProvider.useCallback[selectAll]"], [
        browseData?.items
    ]);
    const clearSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[clearSelection]": ()=>{
            setSelectedItems([]);
        }
    }["FileManagerProvider.useCallback[clearSelection]"], []);
    const updateFilters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[updateFilters]": (newFilters)=>{
            setFilters({
                "FileManagerProvider.useCallback[updateFilters]": (prev)=>({
                        ...prev,
                        ...newFilters
                    })
            }["FileManagerProvider.useCallback[updateFilters]"]);
        }
    }["FileManagerProvider.useCallback[updateFilters]"], []);
    const resetFilters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[resetFilters]": ()=>{
            setFilters(defaultFilters);
        }
    }["FileManagerProvider.useCallback[resetFilters]"], []);
    const uploadFiles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[uploadFiles]": (files, folderPath)=>{
            uploadMutation.mutate({
                files,
                folderPath
            });
        }
    }["FileManagerProvider.useCallback[uploadFiles]"], [
        uploadMutation
    ]);
    const createFolder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[createFolder]": (folderName, parentPath)=>{
            createFolderMutation.mutate({
                parentPath: parentPath || currentPath,
                folderName
            });
        }
    }["FileManagerProvider.useCallback[createFolder]"], [
        createFolderMutation,
        currentPath
    ]);
    const deleteSelectedItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[deleteSelectedItems]": (permanent = false)=>{
            setSelectedItems({
                "FileManagerProvider.useCallback[deleteSelectedItems]": (currentItems)=>{
                    if (currentItems.length > 0) {
                        deleteMutation.mutate({
                            filePaths: currentItems,
                            permanent
                        });
                    }
                    return currentItems; // Keep items until mutation succeeds
                }
            }["FileManagerProvider.useCallback[deleteSelectedItems]"]);
        }
    }["FileManagerProvider.useCallback[deleteSelectedItems]"], [
        deleteMutation
    ]);
    const deleteItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[deleteItem]": (relativePath, permanent = false)=>{
            if (relativePath) {
                deleteMutation.mutate({
                    filePaths: [
                        relativePath
                    ],
                    permanent
                });
            }
        }
    }["FileManagerProvider.useCallback[deleteItem]"], [
        deleteMutation
    ]);
    const moveSelectedItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[moveSelectedItems]": (destinationPath, overwriteExisting = false)=>{
            setSelectedItems({
                "FileManagerProvider.useCallback[moveSelectedItems]": (currentItems)=>{
                    if (currentItems.length > 0) {
                        moveMutation.mutate({
                            sourcePaths: currentItems,
                            destinationPath,
                            overwriteExisting
                        });
                    }
                    return currentItems;
                }
            }["FileManagerProvider.useCallback[moveSelectedItems]"]);
        }
    }["FileManagerProvider.useCallback[moveSelectedItems]"], [
        moveMutation
    ]);
    const moveItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[moveItem]": (sourcePath, destinationPath, overwriteExisting = false)=>{
            if (sourcePath) {
                moveMutation.mutate({
                    sourcePaths: [
                        sourcePath
                    ],
                    destinationPath,
                    overwriteExisting
                });
            }
        }
    }["FileManagerProvider.useCallback[moveItem]"], [
        moveMutation
    ]);
    const copySelectedItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[copySelectedItems]": (destinationPath, overwriteExisting = false)=>{
            setSelectedItems({
                "FileManagerProvider.useCallback[copySelectedItems]": (currentItems)=>{
                    if (currentItems.length > 0) {
                        copyMutation.mutate({
                            sourcePaths: currentItems,
                            destinationPath,
                            overwriteExisting
                        });
                    }
                    return currentItems;
                }
            }["FileManagerProvider.useCallback[copySelectedItems]"]);
        }
    }["FileManagerProvider.useCallback[copySelectedItems]"], [
        copyMutation
    ]);
    const copyItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[copyItem]": (sourcePath, destinationPath, overwriteExisting = false)=>{
            if (sourcePath) {
                copyMutation.mutate({
                    sourcePaths: [
                        sourcePath
                    ],
                    destinationPath,
                    overwriteExisting
                });
            }
        }
    }["FileManagerProvider.useCallback[copyItem]"], [
        copyMutation
    ]);
    // Download file action
    const downloadFile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileManagerProvider.useCallback[downloadFile]": async (relativePath)=>{
            try {
                const fileName = relativePath.split('/').pop() || 'download';
                toast.success({
                    title: "Download Started",
                    description: `Downloading ${fileName}...`
                });
                // Use fetchWithAuth to download the file with authentication
                const downloadUrl = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fileManagerService"].getDownloadUrl(relativePath);
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchWithAuth"])(downloadUrl);
                if (!response.ok) {
                    throw new Error(`Download failed: ${response.status} ${response.statusText}`);
                }
                // Get the file blob
                const blob = await response.blob();
                // Create blob URL and trigger download
                const blobUrl = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = blobUrl;
                link.setAttribute('download', fileName);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                // Clean up the blob URL
                window.URL.revokeObjectURL(blobUrl);
                toast.success({
                    title: "Download Complete",
                    description: `${fileName} downloaded successfully.`
                });
            } catch (error) {
                toast.error({
                    title: "Download Failed",
                    description: error instanceof Error ? error.message : "Could not download file."
                });
                console.error("Error downloading file:", error);
            }
        }
    }["FileManagerProvider.useCallback[downloadFile]"], [
        toast
    ]);
    // Context value
    const value = {
        // Data
        browseData,
        folderStructure,
        rootFolderStructure,
        // State
        currentPath,
        selectedItems,
        viewMode,
        filters,
        uploadProgress,
        // Loading states
        isLoading: isBrowseLoading || isFoldersLoading || isRootFoldersLoading,
        isBrowseLoading,
        isFoldersLoading,
        isRootFoldersLoading,
        isUploading: uploadMutation.isPending,
        isCreatingFolder: createFolderMutation.isPending,
        isDeleting: deleteMutation.isPending,
        isMoving: moveMutation.isPending,
        isCopying: copyMutation.isPending,
        // Error states
        error: browseError,
        // Actions
        navigateToPath,
        navigateUp,
        selectItem,
        selectAll,
        clearSelection,
        setViewMode,
        updateFilters,
        resetFilters,
        uploadFiles,
        createFolder,
        deleteSelectedItems,
        deleteItem,
        moveSelectedItems,
        moveItem,
        copySelectedItems,
        copyItem,
        downloadFile,
        refetchBrowse,
        refetchFolders,
        refetchRootFolders,
        // Computed
        hasSelection: selectedItems.length > 0,
        selectedCount: selectedItems.length,
        canNavigateUp: !!currentPath
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FileManagerContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/contexts/FileManagerContext.tsx",
        lineNumber: 571,
        columnNumber: 5
    }, this);
};
_s(FileManagerProvider, "fRxLfQOj7KFNPMmkFdiDeUb+//U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
_c = FileManagerProvider;
const useFileManagerContext = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(FileManagerContext);
    if (!context) {
        throw new Error('useFileManagerContext must be used within a FileManagerProvider');
    }
    return context;
};
_s1(useFileManagerContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "FileManagerProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/dialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Dialog": (()=>Dialog),
    "DialogClose": (()=>DialogClose),
    "DialogContent": (()=>DialogContent),
    "DialogDescription": (()=>DialogDescription),
    "DialogFooter": (()=>DialogFooter),
    "DialogHeader": (()=>DialogHeader),
    "DialogOverlay": (()=>DialogOverlay),
    "DialogPortal": (()=>DialogPortal),
    "DialogTitle": (()=>DialogTitle),
    "DialogTrigger": (()=>DialogTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-dialog/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as XIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
function Dialog({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "dialog",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
_c = Dialog;
function DialogTrigger({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"], {
        "data-slot": "dialog-trigger",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 18,
        columnNumber: 10
    }, this);
}
_c1 = DialogTrigger;
function DialogPortal({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Portal"], {
        "data-slot": "dialog-portal",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 24,
        columnNumber: 10
    }, this);
}
_c2 = DialogPortal;
function DialogClose({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"], {
        "data-slot": "dialog-close",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 30,
        columnNumber: 10
    }, this);
}
_c3 = DialogClose;
function DialogOverlay({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"], {
        "data-slot": "dialog-overlay",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_c4 = DialogOverlay;
function DialogContent({ className, children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogPortal, {
        "data-slot": "dialog-portal",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogOverlay, {}, void 0, false, {
                fileName: "[project]/components/ui/dialog.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
                "data-slot": "dialog-content",
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg", className),
                ...props,
                children: [
                    children,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"], {
                        className: "ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XIcon$3e$__["XIcon"], {}, void 0, false, {
                                fileName: "[project]/components/ui/dialog.tsx",
                                lineNumber: 67,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "sr-only",
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/components/ui/dialog.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ui/dialog.tsx",
                        lineNumber: 66,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ui/dialog.tsx",
                lineNumber: 57,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_c5 = DialogContent;
function DialogHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "dialog-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col gap-2 text-center sm:text-left", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
_c6 = DialogHeader;
function DialogFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "dialog-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
}
_c7 = DialogFooter;
function DialogTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Title"], {
        "data-slot": "dialog-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-lg leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 103,
        columnNumber: 5
    }, this);
}
_c8 = DialogTitle;
function DialogDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Description"], {
        "data-slot": "dialog-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this);
}
_c9 = DialogDescription;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;
__turbopack_context__.k.register(_c, "Dialog");
__turbopack_context__.k.register(_c1, "DialogTrigger");
__turbopack_context__.k.register(_c2, "DialogPortal");
__turbopack_context__.k.register(_c3, "DialogClose");
__turbopack_context__.k.register(_c4, "DialogOverlay");
__turbopack_context__.k.register(_c5, "DialogContent");
__turbopack_context__.k.register(_c6, "DialogHeader");
__turbopack_context__.k.register(_c7, "DialogFooter");
__turbopack_context__.k.register(_c8, "DialogTitle");
__turbopack_context__.k.register(_c9, "DialogDescription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:cursor-pointer", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;
;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/confirmation-dialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ConfirmationDialogProvider": (()=>ConfirmationDialogProvider),
    "useConfirmationDialog": (()=>useConfirmationDialog)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-toast.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
// Context
const ConfirmationDialogContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ConfirmationDialogProvider({ children }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [options, setOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [status, setStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("idle");
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [resolveRef, setResolveRef] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { success, error: toastError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const confirm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ConfirmationDialogProvider.useCallback[confirm]": (options)=>{
            setOptions(options);
            setIsOpen(true);
            setStatus("idle");
            setError(null);
            return new Promise({
                "ConfirmationDialogProvider.useCallback[confirm]": (resolve)=>{
                    setResolveRef({
                        "ConfirmationDialogProvider.useCallback[confirm]": ()=>resolve
                    }["ConfirmationDialogProvider.useCallback[confirm]"]);
                }
            }["ConfirmationDialogProvider.useCallback[confirm]"]);
        }
    }["ConfirmationDialogProvider.useCallback[confirm]"], []);
    const handleConfirm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ConfirmationDialogProvider.useCallback[handleConfirm]": async ()=>{
            if (!options?.onConfirm) {
                if (resolveRef) resolveRef(true);
                setIsOpen(false);
                return;
            }
            try {
                setIsLoading(true);
                setStatus("loading");
                await options.onConfirm();
                setStatus("success");
                success({
                    title: "Success",
                    description: "Operation completed successfully!"
                });
                if (resolveRef) resolveRef(true);
                setTimeout({
                    "ConfirmationDialogProvider.useCallback[handleConfirm]": ()=>{
                        setIsOpen(false);
                        setIsLoading(false);
                    }
                }["ConfirmationDialogProvider.useCallback[handleConfirm]"], 1000);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "An error occurred";
                setStatus("error");
                setError(errorMessage);
                toastError({
                    title: "Error",
                    description: `Error: ${errorMessage}`
                });
                setIsLoading(false);
                if (resolveRef) resolveRef(false);
            }
        }
    }["ConfirmationDialogProvider.useCallback[handleConfirm]"], [
        options,
        resolveRef,
        success,
        toastError
    ]);
    const handleCancel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ConfirmationDialogProvider.useCallback[handleCancel]": ()=>{
            if (options?.onCancel) {
                options.onCancel();
            }
            if (resolveRef) resolveRef(false);
            setIsOpen(false);
        }
    }["ConfirmationDialogProvider.useCallback[handleCancel]"], [
        options,
        resolveRef
    ]);
    const closeDialog = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ConfirmationDialogProvider.useCallback[closeDialog]": ()=>{
            if (isLoading) return;
            setIsOpen(false);
            if (resolveRef) resolveRef(false);
        }
    }["ConfirmationDialogProvider.useCallback[closeDialog]"], [
        isLoading,
        resolveRef
    ]);
    // Dialog Component
    const ConfirmationDialogComponent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ConfirmationDialogProvider.useCallback[ConfirmationDialogComponent]": ()=>{
            if (!options) return null;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dialog"], {
                open: isOpen,
                onOpenChange: {
                    "ConfirmationDialogProvider.useCallback[ConfirmationDialogComponent]": (open)=>!open && closeDialog()
                }["ConfirmationDialogProvider.useCallback[ConfirmationDialogComponent]"],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-[425px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: options.title
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 117,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: options.message
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 118,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/ui/confirmation-dialog.tsx",
                            lineNumber: 116,
                            columnNumber: 11
                        }, this),
                        status === "error" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2 p-3 text-sm border rounded-md bg-destructive/10 text-destructive border-destructive/20",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: error || "An error occurred"
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 124,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/ui/confirmation-dialog.tsx",
                            lineNumber: 122,
                            columnNumber: 13
                        }, this),
                        status === "success" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2 p-3 text-sm border rounded-md bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900/30",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 130,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Operation completed successfully"
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 131,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/ui/confirmation-dialog.tsx",
                            lineNumber: 129,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            className: "gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    onClick: handleCancel,
                                    disabled: isLoading,
                                    children: options.cancelText || "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: options.variant || "default",
                                    onClick: handleConfirm,
                                    disabled: isLoading || status === "success",
                                    children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                className: "w-4 h-4 mr-2 animate-spin"
                                            }, void 0, false, {
                                                fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                                lineNumber: 146,
                                                columnNumber: 19
                                            }, this),
                                            "Processing..."
                                        ]
                                    }, void 0, true) : options.confirmText || "Confirm"
                                }, void 0, false, {
                                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                                    lineNumber: 139,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/ui/confirmation-dialog.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/ui/confirmation-dialog.tsx",
                    lineNumber: 115,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/ui/confirmation-dialog.tsx",
                lineNumber: 114,
                columnNumber: 7
            }, this);
        }
    }["ConfirmationDialogProvider.useCallback[ConfirmationDialogComponent]"], [
        isOpen,
        options,
        status,
        error,
        isLoading,
        closeDialog,
        handleCancel,
        handleConfirm
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ConfirmationDialogContext.Provider, {
        value: {
            confirm,
            closeDialog,
            options,
            isOpen,
            isLoading,
            status,
            error
        },
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ConfirmationDialogComponent, {}, void 0, false, {
                fileName: "[project]/components/ui/confirmation-dialog.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/confirmation-dialog.tsx",
        lineNumber: 160,
        columnNumber: 5
    }, this);
}
_s(ConfirmationDialogProvider, "m/kaDbsOfS3XqxH6tt2wiaXPK8s=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ConfirmationDialogProvider;
function useConfirmationDialog() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ConfirmationDialogContext);
    if (context === undefined) {
        throw new Error("useConfirmationDialog must be used within a ConfirmationDialogProvider");
    }
    return context;
}
_s1(useConfirmationDialog, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ConfirmationDialogProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Providers": (()=>Providers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$ToastProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/providers/ToastProvider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$CategoryStoreProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/providers/CategoryStoreProvider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$FileManagerContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/contexts/FileManagerContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$confirmation$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/confirmation-dialog.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function Providers({ children }) {
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "Providers.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]()
    }["Providers.useState"]);
    return(// Wrap with SessionProvider
    // Temporarily disable SessionProvider to debug
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$CategoryStoreProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CategoryStoreProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$ToastProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ToastProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$FileManagerContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileManagerProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$confirmation$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfirmationDialogProvider"], {
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/lib/providers.tsx",
                        lineNumber: 21,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/lib/providers.tsx",
                    lineNumber: 20,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/lib/providers.tsx",
                lineNumber: 19,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/lib/providers.tsx",
            lineNumber: 18,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/lib/providers.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this));
}
_s(Providers, "qFhNRSk+4hqflxYLL9+zYF5AcuQ=");
_c = Providers;
var _c;
__turbopack_context__.k.register(_c, "Providers");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_a6dd4be9._.js.map