{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatCurrency (value: number) {\r\n  return new Intl.NumberFormat(\"en-US\", {\r\n    style: \"currency\",\r\n    currency: \"USD\",\r\n  }).format(value)\r\n}\r\n\r\n/**\r\n * Converts a relative image path to a full URL\r\n *\r\n * @param path - Relative image path or full URL\r\n * @returns Full image URL\r\n */\r\nexport function getImageUrl(path: string | null | undefined): string {\r\n  const baseUrl = process.env.NEXT_PUBLIC_IMAGE_BASE_URL || '';\r\n\r\n  // If path is null, undefined or empty string, return default image or empty string\r\n  if (!path) {\r\n    return ''; // Or return a default image URL\r\n  }\r\n\r\n  // If path is already a full URL (starts with http:// or https://), return as is\r\n  if (path.startsWith('http://') || path.startsWith('https://')) {\r\n    return path;\r\n  }\r\n  // Check duplicate /uploads path\r\n  path.replace('/uploads/uploads', '/uploads')\r\n\r\n  // If path starts with /, remove it to avoid duplication with baseUrl\r\n  const normalizedPath = path.startsWith('/') ? path.substring(1) : path;\r\n\r\n  // Combine baseUrl and path\r\n  return `${baseUrl}${normalizedPath}`;\r\n}\r\n\r\nexport function isEmptyString(value: string | null | undefined): boolean {\r\n  return value === null || value === undefined || value.trim() === '';\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;AAqBkB;AArBlB;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAgB,KAAa;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,YAAY,IAA+B;IACzD,MAAM,UAAU,sEAA0C;IAE1D,mFAAmF;IACnF,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,gCAAgC;IAC7C;IAEA,gFAAgF;IAChF,IAAI,KAAK,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC,aAAa;QAC7D,OAAO;IACT;IACA,gCAAgC;IAChC,KAAK,OAAO,CAAC,oBAAoB;IAEjC,qEAAqE;IACrE,MAAM,iBAAiB,KAAK,UAAU,CAAC,OAAO,KAAK,SAAS,CAAC,KAAK;IAElE,2BAA2B;IAC3B,OAAO,GAAG,UAAU,gBAAgB;AACtC;AAEO,SAAS,cAAc,KAAgC;IAC5D,OAAO,UAAU,QAAQ,UAAU,aAAa,MAAM,IAAI,OAAO;AACnE", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/providers/ToastProvider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport type React from \"react\"\r\nimport { createContext, useContext, useState, useCallback, useEffect } from \"react\"\r\nimport { X } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport type ToastType = \"success\" | \"error\" | \"warning\" | \"info\"\r\n\r\nexport interface ToastProps {\r\n    id: string\r\n    title?: string\r\n    description?: string\r\n    type?: ToastType\r\n    duration?: number\r\n    action?: React.ReactNode\r\n}\r\n\r\ninterface ToastContextProps {\r\n    toasts: ToastProps[]\r\n    addToast: (toast: Omit<ToastProps, \"id\">) => string\r\n    removeToast: (id: string) => void\r\n    updateToast: (id: string, toast: Partial<ToastProps>) => void\r\n}\r\n\r\nconst ToastContext = createContext<ToastContextProps | undefined>(undefined)\r\n\r\nexport const useToast = () => {\r\n    const context = useContext(ToastContext)\r\n    if (!context) {\r\n        throw new Error(\"useToast must be used within a ToastProvider\")\r\n    }\r\n    return context\r\n}\r\n\r\nexport const ToastProvider = ({ children }: { children: React.ReactNode }) => {\r\n    const [toasts, setToasts] = useState<ToastProps[]>([])\r\n\r\n    const addToast = useCallback((toast: Omit<ToastProps, \"id\">) => {\r\n        const id = Math.random().toString(36).substring(2, 9)\r\n        setToasts((prevToasts) => [...prevToasts, { id, ...toast }])\r\n        return id\r\n    }, [])\r\n\r\n    const removeToast = useCallback((id: string) => {\r\n        setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id))\r\n    }, [])\r\n\r\n    const updateToast = useCallback((id: string, toast: Partial<ToastProps>) => {\r\n        setToasts((prevToasts) => prevToasts.map((t) => (t.id === id ? { ...t, ...toast } : t)))\r\n    }, [])\r\n\r\n    return (\r\n        <ToastContext.Provider value={{ toasts, addToast, removeToast, updateToast }}>\r\n            {children}\r\n            <ToastContainer />\r\n        </ToastContext.Provider>\r\n    )\r\n}\r\n\r\nconst ToastContainer = () => {\r\n    const { toasts, removeToast } = useToast()\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-4 right-4 z-200 flex flex-col gap-2 p-4 max-h-screen overflow-hidden pointer-events-none\"\r\n            aria-live=\"polite\"\r\n            role=\"region\"\r\n            aria-label=\"Notifications\"\r\n        >\r\n            {toasts.map((toast) => (\r\n                <ToastItem key={toast.id} toast={toast} onClose={() => removeToast(toast.id)} />\r\n            ))}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ToastItem = ({ toast, onClose }: { toast: ToastProps; onClose: () => void }) => {\r\n    const { id, title, description, type = \"info\", duration = 8000, action } = toast\r\n\r\n    useEffect(() => {\r\n        if (duration === Number.POSITIVE_INFINITY) return\r\n\r\n        const timer = setTimeout(() => {\r\n            onClose()\r\n        }, duration)\r\n\r\n        return () => clearTimeout(timer)\r\n    }, [duration, onClose])\r\n\r\n    const getTypeStyles = () => {\r\n        switch (type) {\r\n            case \"success\":\r\n                return \"bg-green-100 border-green-500 text-green-800 dark:bg-green-900/50 dark:border-green-600 dark:text-green-100\"\r\n            case \"error\":\r\n                return \"bg-red-100 border-red-500 text-red-800 dark:bg-red-900/50 dark:border-red-600 dark:text-red-100\"\r\n            case \"warning\":\r\n                return \"bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/50 dark:border-yellow-600 dark:text-yellow-100\"\r\n            case \"info\":\r\n            default:\r\n                return \"bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/50 dark:border-blue-600 dark:text-blue-100\"\r\n        }\r\n    }\r\n\r\n    return (\r\n        <div\r\n            className={cn(\r\n                \"animate-slide-in pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border p-4 shadow-md transition-all\",\r\n                getTypeStyles(),\r\n            )}\r\n            role=\"alert\"\r\n            aria-labelledby={`toast-${id}-title`}\r\n            data-state=\"open\"\r\n            data-toast-type={type}\r\n        >\r\n            <div className=\"flex items-start justify-between\">\r\n                <div className=\"flex-1\">\r\n                    {title && (\r\n                        <h3 id={`toast-${id}-title`} className=\"font-medium\">\r\n                            {title}\r\n                        </h3>\r\n                    )}\r\n                    {description && <div className=\"mt-1 text-sm opacity-90\">{description}</div>}\r\n                    {action && <div className=\"mt-2\">{action}</div>}\r\n                </div>\r\n                <button\r\n                    onClick={onClose}\r\n                    className=\"ml-4 inline-flex h-6 w-6 shrink-0 items-center justify-center rounded-md text-current opacity-50 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-current\"\r\n                    aria-label=\"Close notification\"\r\n                >\r\n                    <X className=\"h-4 w-4\" />\r\n                </button>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;;;AALA;;;;AAyBA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE3D,MAAM,WAAW;;IACpB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;GANa;AAQN,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAErD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAC1B,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YACnD;uDAAU,CAAC,aAAe;2BAAI;wBAAY;4BAAE;4BAAI,GAAG,KAAK;wBAAC;qBAAE;;YAC3D,OAAO;QACX;8CAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC7B;0DAAU,CAAC,aAAe,WAAW,MAAM;kEAAC,CAAC,QAAU,MAAM,EAAE,KAAK;;;QACxE;iDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,IAAY;YACzC;0DAAU,CAAC,aAAe,WAAW,GAAG;kEAAC,CAAC,IAAO,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,KAAK;4BAAC,IAAI;;;QACxF;iDAAG,EAAE;IAEL,qBACI,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAU;YAAa;QAAY;;YACtE;0BACD,6LAAC;;;;;;;;;;;AAGb;IAvBa;KAAA;AAyBb,MAAM,iBAAiB;;IACnB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IAEhC,qBACI,6LAAC;QACG,WAAU;QACV,aAAU;QACV,MAAK;QACL,cAAW;kBAEV,OAAO,GAAG,CAAC,CAAC,sBACT,6LAAC;gBAAyB,OAAO;gBAAO,SAAS,IAAM,YAAY,MAAM,EAAE;eAA3D,MAAM,EAAE;;;;;;;;;;AAIxC;IAfM;;QAC8B;;;MAD9B;AAiBN,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,OAAO,EAA8C;;IAC7E,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,MAAM,EAAE,WAAW,IAAI,EAAE,MAAM,EAAE,GAAG;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,aAAa,OAAO,iBAAiB,EAAE;YAE3C,MAAM,QAAQ;6CAAW;oBACrB;gBACJ;4CAAG;YAEH;uCAAO,IAAM,aAAa;;QAC9B;8BAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,gBAAgB;QAClB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;YACL;gBACI,OAAO;QACf;IACJ;IAEA,qBACI,6LAAC;QACG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACR,uHACA;QAEJ,MAAK;QACL,mBAAiB,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC;QACpC,cAAW;QACX,mBAAiB;kBAEjB,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;wBACV,uBACG,6LAAC;4BAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC;4BAAE,WAAU;sCAClC;;;;;;wBAGR,6BAAe,6LAAC;4BAAI,WAAU;sCAA2B;;;;;;wBACzD,wBAAU,6LAAC;4BAAI,WAAU;sCAAQ;;;;;;;;;;;;8BAEtC,6LAAC;oBACG,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKjC;IA1DM;MAAA", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/lib/api-utils.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { getSession, signOut } from \"next-auth/react\";\r\n\r\nexport const API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5000\";\r\n\r\n/**\r\n * Lấy token xác thực từ NextAuth session\r\n * @returns Token xác thực hoặc undefined nếu chưa đăng nhập\r\n */\r\nexport async function getAuthToken(): Promise<string | undefined> {\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  const session:any = await getSession();\r\n  return session?.user?.accessToken;\r\n}\r\n\r\n/**\r\n * Lấy header xác thực cho các request API\r\n * @returns Object chứa header Authorization\r\n */\r\nexport async function getAuthHeader(): Promise<HeadersInit> {\r\n  const token = await getAuthToken();\r\n  \r\n  return {\r\n    \"Content-Type\": \"application/json\",\r\n    ...(token ? { Authorization: `Bearer ${token}` } : {}),\r\n  };\r\n}\r\n\r\n/**\r\n * Wrapper cho fetch API với xác thực tự động\r\n * @param url URL của API endpoint\r\n * @param options Các options cho fetch API\r\n * @returns Response từ fetch API\r\n */\r\nexport async function fetchWithAuth(\r\n  url: string, \r\n  options: RequestInit = {}\r\n): Promise<Response> {\r\n  const headers = await getAuthHeader();\r\n  \r\n  const response = await fetch(url, {\r\n    ...options,\r\n    headers: {\r\n      ...headers,\r\n      ...options.headers,\r\n    },\r\n  });\r\n  \r\n  // Xử lý lỗi xác thực\r\n  if (response.status === 401) {\r\n    // Đăng xuất người dùng khi token không hợp lệ hoặc hết hạn\r\n    await signOut({ redirect: true, callbackUrl: \"/login\" });\r\n    throw new Error(\"Session expired. Please login again.\");\r\n  }\r\n  \r\n  return response;\r\n}\r\n\r\n/**\r\n * Wrapper cho fetchWithAuth với FormData\r\n * @param url URL của API endpoint\r\n * @param formData FormData để gửi\r\n * @param method HTTP method (mặc định là POST)\r\n * @returns Response từ fetch API\r\n */\r\nexport async function fetchWithAuthFormData(\r\n  url: string,\r\n  formData: FormData,\r\n  method: string = \"POST\"\r\n): Promise<Response> {\r\n  const token = await getAuthToken();\r\n  \r\n  const headers: HeadersInit = {};\r\n  if (token) {\r\n    headers[\"Authorization\"] = `Bearer ${token}`;\r\n  }\r\n  \r\n  const response = await fetch(url, {\r\n    method,\r\n    headers,\r\n    body: formData,\r\n  });\r\n  \r\n  // Xử lý lỗi xác thực\r\n  if (response.status === 401) {\r\n    await signOut({ redirect: true, callbackUrl: \"/login\" });\r\n    throw new Error(\"Session expired. Please login again.\");\r\n  }\r\n  \r\n  return response;\r\n}\r\n\r\n/**\r\n * Lấy thông tin người dùng hiện tại từ session\r\n * @returns Thông tin người dùng hoặc null nếu chưa đăng nhập\r\n */\r\nexport async function getCurrentUser() {\r\n  const session = await getSession();\r\n  return session?.user || null;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAIuB;AAFvB;AAFA;;AAIO,MAAM,UAAU,6DAAmC;AAMnD,eAAe;IACpB,8DAA8D;IAC9D,MAAM,UAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,OAAO,SAAS,MAAM;AACxB;AAMO,eAAe;IACpB,MAAM,QAAQ,MAAM;IAEpB,OAAO;QACL,gBAAgB;QAChB,GAAI,QAAQ;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,IAAI,CAAC,CAAC;IACvD;AACF;AAQO,eAAe,cACpB,GAAW,EACX,UAAuB,CAAC,CAAC;IAEzB,MAAM,UAAU,MAAM;IAEtB,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,GAAG,OAAO;QACV,SAAS;YACP,GAAG,OAAO;YACV,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,qBAAqB;IACrB,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,2DAA2D;QAC3D,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;YAAM,aAAa;QAAS;QACtD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AASO,eAAe,sBACpB,GAAW,EACX,QAAkB,EAClB,SAAiB,MAAM;IAEvB,MAAM,QAAQ,MAAM;IAEpB,MAAM,UAAuB,CAAC;IAC9B,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC;QACA;QACA,MAAM;IACR;IAEA,qBAAqB;IACrB,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;YAAM,aAAa;QAAS;QACtD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAMO,eAAe;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC/B,OAAO,SAAS,QAAQ;AAC1B", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/lib/query-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/**\r\n * Utility functions for building API query parameters\r\n */\r\n\r\n/**\r\n * Builds query string from filter parameters\r\n * @param params - Object containing filter parameters\r\n * @returns URLSearchParams object\r\n */\r\nexport function buildQueryParams(params: Record<string, any>): URLSearchParams {\r\n  const searchParams = new URLSearchParams();\r\n\r\n  Object.entries(params).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== '') {\r\n      // Handle boolean values\r\n      if (typeof value === 'boolean') {\r\n        searchParams.append(key, value.toString());\r\n      }\r\n      // Handle number values\r\n      else if (typeof value === 'number') {\r\n        searchParams.append(key, value.toString());\r\n      }\r\n      // Handle string values\r\n      else if (typeof value === 'string') {\r\n        searchParams.append(key, value);\r\n      }\r\n      // Handle Date objects\r\n      else if (value instanceof Date) {\r\n        searchParams.append(key, value.toISOString());\r\n      }\r\n    }\r\n  });\r\n\r\n  return searchParams;\r\n}\r\n\r\n/**\r\n * Builds query string URL from base URL and parameters\r\n * @param baseUrl - Base API URL\r\n * @param params - Filter parameters\r\n * @returns Complete URL with query string\r\n */\r\nexport function buildApiUrl(baseUrl: string, params: Record<string, any>): string {\r\n  const queryParams = buildQueryParams(params);\r\n  const queryString = queryParams.toString();\r\n\r\n  if (queryString) {\r\n    const separator = baseUrl.includes('?') ? '&' : '?';\r\n    return `${baseUrl}${separator}${queryString}`;\r\n  }\r\n\r\n  return baseUrl;\r\n}\r\n\r\n/**\r\n * Removes empty or undefined values from an object\r\n * @param obj - Object to clean\r\n * @returns Cleaned object\r\n */\r\nexport function cleanFilters<T extends Record<string, any>>(obj: T): Partial<T> {\r\n  const cleaned: Partial<T> = {};\r\n\r\n  Object.entries(obj).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== '') {\r\n      cleaned[key as keyof T] = value;\r\n    }\r\n  });\r\n\r\n  return cleaned;\r\n}\r\n\r\n/**\r\n * Debounce function for search inputs\r\n * @param func - Function to debounce\r\n * @param wait - Wait time in milliseconds\r\n * @returns Debounced function\r\n */\r\nexport function debounce<T extends (...args: any[]) => any>(\r\n  func: T,\r\n  wait: number\r\n): (...args: Parameters<T>) => void {\r\n  let timeout: NodeJS.Timeout;\r\n\r\n  return (...args: Parameters<T>) => {\r\n    clearTimeout(timeout);\r\n    timeout = setTimeout(() => func(...args), wait);\r\n  };\r\n}\r\n\r\n/**\r\n * Checks if any filters are active (non-empty)\r\n * @param filters - Filter object\r\n * @returns Boolean indicating if filters are active\r\n */\r\nexport function hasActiveFilters(filters: Record<string, any>): boolean {\r\n  return Object.values(filters).some(value =>\r\n    value !== undefined &&\r\n    value !== null &&\r\n    value !== '' &&\r\n    !(Array.isArray(value) && value.length === 0)\r\n  );\r\n}\r\n\r\n/**\r\n * Resets pagination to first page when filters change\r\n * @param filters - Current filters\r\n * @param previousFilters - Previous filters\r\n * @returns Boolean indicating if pagination should reset\r\n */\r\nexport function shouldResetPagination(\r\n  filters: Record<string, any>,\r\n  previousFilters: Record<string, any>\r\n): boolean {\r\n  // Compare all filter values except pagination-related ones\r\n  const filterKeys = Object.keys(filters).filter(key =>\r\n    !['pageNumber', 'pageSize', 'skip'].includes(key)\r\n  );\r\n\r\n  return filterKeys.some(key => filters[key] !== previousFilters[key]);\r\n}\r\n\r\n/**\r\n * Formats date for API consumption (ISO string)\r\n * @param date - Date object or string\r\n * @returns ISO string or undefined\r\n */\r\nexport function formatDateForApi(date: Date | string | undefined): string | undefined {\r\n  if (!date) return undefined;\r\n\r\n  if (typeof date === 'string') {\r\n    return new Date(date).toISOString();\r\n  }\r\n\r\n  return date.toISOString();\r\n}\r\n\r\n/**\r\n * Parses date from API response\r\n * @param dateString - ISO date string\r\n * @returns Date object or undefined\r\n */\r\nexport function parseDateFromApi(dateString: string | undefined): Date | undefined {\r\n  if (!dateString) return undefined;\r\n  return new Date(dateString);\r\n}\r\n\r\n/**\r\n * Creates default pagination parameters\r\n * @param pageSize - Default page size\r\n * @returns Default pagination params\r\n */\r\nexport function createDefaultPagination(pageSize: number = 25) {\r\n  return {\r\n    pageNumber: 1,\r\n    pageSize,\r\n    sortBy: 'createdAt',\r\n    sortDirection: 'desc',\r\n    isDescending: true,\r\n  };\r\n}\r\n\r\n/**\r\n * Validates pagination parameters\r\n * @param params - Pagination parameters\r\n * @returns Validated parameters\r\n */\r\nexport function validatePaginationParams(params: {\r\n  pageNumber?: number;\r\n  pageSize?: number;\r\n  sortBy?: string;\r\n  sortDirection?: string;\r\n  isDescending?: boolean;\r\n}) {\r\n  return {\r\n    pageNumber: Math.max(1, params.pageNumber || 1),\r\n    pageSize: Math.min(100, Math.max(1, params.pageSize || 25)),\r\n    sortBy: params.sortBy || 'createdAt',\r\n    sortDirection: params.sortDirection || 'desc',\r\n    isDescending: params.isDescending ?? true,\r\n  };\r\n}\r\n\r\n/**\r\n * Safely handles Select component values to avoid empty string errors\r\n * @param value - The current filter value\r\n * @param defaultValue - Default value to use when no selection (default: \"all\")\r\n * @returns Safe value for Select component\r\n */\r\nexport function getSelectValue(value: string | number | undefined | null, defaultValue: string = \"all\"): string {\r\n  if (value === undefined || value === null || value === \"\") {\r\n    return defaultValue;\r\n  }\r\n  return value.toString();\r\n}\r\n\r\n/**\r\n * Parses Select component value back to appropriate type\r\n * @param value - Value from Select component\r\n * @param defaultValue - Default value that represents \"no selection\"\r\n * @param type - Type to parse to ('string', 'number', 'float')\r\n * @returns Parsed value or undefined if default\r\n */\r\nexport function parseSelectValue(\r\n  value: string,\r\n  defaultValue: string = \"all\",\r\n  type: 'string' | 'number' | 'float' = 'string'\r\n): string | number | undefined {\r\n  if (value === defaultValue) {\r\n    return undefined;\r\n  }\r\n\r\n  switch (type) {\r\n    case 'number':\r\n      return parseInt(value);\r\n    case 'float':\r\n      return parseFloat(value);\r\n    case 'string':\r\n    default:\r\n      return value;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD;;CAEC,GAED;;;;CAIC;;;;;;;;;;;;;;AACM,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,wBAAwB;YACxB,IAAI,OAAO,UAAU,WAAW;gBAC9B,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;YACzC,OAEK,IAAI,OAAO,UAAU,UAAU;gBAClC,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;YACzC,OAEK,IAAI,OAAO,UAAU,UAAU;gBAClC,aAAa,MAAM,CAAC,KAAK;YAC3B,OAEK,IAAI,iBAAiB,MAAM;gBAC9B,aAAa,MAAM,CAAC,KAAK,MAAM,WAAW;YAC5C;QACF;IACF;IAEA,OAAO;AACT;AAQO,SAAS,YAAY,OAAe,EAAE,MAA2B;IACtE,MAAM,cAAc,iBAAiB;IACrC,MAAM,cAAc,YAAY,QAAQ;IAExC,IAAI,aAAa;QACf,MAAM,YAAY,QAAQ,QAAQ,CAAC,OAAO,MAAM;QAChD,OAAO,GAAG,UAAU,YAAY,aAAa;IAC/C;IAEA,OAAO;AACT;AAOO,SAAS,aAA4C,GAAM;IAChE,MAAM,UAAsB,CAAC;IAE7B,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACvC,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,OAAO,CAAC,IAAe,GAAG;QAC5B;IACF;IAEA,OAAO;AACT;AAQO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAOO,SAAS,iBAAiB,OAA4B;IAC3D,OAAO,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,QACjC,UAAU,aACV,UAAU,QACV,UAAU,MACV,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,CAAC;AAEhD;AAQO,SAAS,sBACd,OAA4B,EAC5B,eAAoC;IAEpC,2DAA2D;IAC3D,MAAM,aAAa,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,CAAA,MAC7C,CAAC;YAAC;YAAc;YAAY;SAAO,CAAC,QAAQ,CAAC;IAG/C,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,OAAO,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI;AACrE;AAOO,SAAS,iBAAiB,IAA+B;IAC9D,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,IAAI,KAAK,MAAM,WAAW;IACnC;IAEA,OAAO,KAAK,WAAW;AACzB;AAOO,SAAS,iBAAiB,UAA8B;IAC7D,IAAI,CAAC,YAAY,OAAO;IACxB,OAAO,IAAI,KAAK;AAClB;AAOO,SAAS,wBAAwB,WAAmB,EAAE;IAC3D,OAAO;QACL,YAAY;QACZ;QACA,QAAQ;QACR,eAAe;QACf,cAAc;IAChB;AACF;AAOO,SAAS,yBAAyB,MAMxC;IACC,OAAO;QACL,YAAY,KAAK,GAAG,CAAC,GAAG,OAAO,UAAU,IAAI;QAC7C,UAAU,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO,QAAQ,IAAI;QACvD,QAAQ,OAAO,MAAM,IAAI;QACzB,eAAe,OAAO,aAAa,IAAI;QACvC,cAAc,OAAO,YAAY,IAAI;IACvC;AACF;AAQO,SAAS,eAAe,KAAyC,EAAE,eAAuB,KAAK;IACpG,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;QACzD,OAAO;IACT;IACA,OAAO,MAAM,QAAQ;AACvB;AASO,SAAS,iBACd,KAAa,EACb,eAAuB,KAAK,EAC5B,OAAsC,QAAQ;IAE9C,IAAI,UAAU,cAAc;QAC1B,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,OAAO,SAAS;QAClB,KAAK;YACH,OAAO,WAAW;QACpB,KAAK;QACL;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/services/categories.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { API_URL, fetchWithAuth } from \"@/lib/api-utils\";\r\nimport { buildApiUrl, cleanFilters } from \"@/lib/query-utils\";\r\nimport { CategoryFilters, PagedResult } from \"@/types/api\";\r\n\r\n/**\r\n * Category response DTO from API\r\n */\r\nexport interface Category {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  description: string | null;\r\n  parentId: number | null;\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  // Additional fields from MCP\r\n  imageUrl: string | null;\r\n  displayOrder: number;\r\n  products?: Product[];\r\n  childCategories?: Category[];\r\n  parentCategory?: Category | null;\r\n}\r\n\r\n/**\r\n * Category DTO with additional computed fields\r\n */\r\nexport interface CategoryDTO extends Category {\r\n  subcategories?: CategoryDTO[];\r\n  parentName?: string;\r\n  productCount?: number;\r\n}\r\n\r\n/**\r\n * Product summary DTO for category relationships\r\n */\r\nexport interface Product {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  price: number;\r\n  imageUrl: string | null;\r\n}\r\n\r\n/**\r\n * Create category request DTO\r\n */\r\nexport interface CreateCategoryPayload {\r\n  name: string;\r\n  slug: string;\r\n  description?: string;\r\n  parentId?: number | null;\r\n  isActive?: boolean;\r\n  displayOrder?: number;\r\n  imageId?: number;\r\n}\r\n\r\n/**\r\n * Update category request DTO\r\n */\r\nexport interface UpdateCategoryPayload extends Partial<CreateCategoryPayload> {\r\n  id: number;\r\n}\r\n\r\n/**\r\n * Get categories with pagination and filtering\r\n * @param filters Category filters\r\n * @returns Paged result of categories\r\n * @endpoint GET /api/Category\r\n */\r\nexport async function getCategories(filters?: CategoryFilters): Promise<PagedResult<CategoryDTO>> {\r\n  try {\r\n    const cleanedFilters = filters ? cleanFilters(filters) : {};\r\n    const url = buildApiUrl(`${API_URL}/api/Category`, cleanedFilters);\r\n    const response = await fetchWithAuth(url);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch categories\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Get categories error:\", error);\r\n    throw new Error(\"Unable to fetch categories. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get all categories without pagination\r\n * @returns List of all categories\r\n * @endpoint GET /api/Category/all\r\n */\r\nexport async function getAllCategories(): Promise<CategoryDTO[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/all`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch categories\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Get all categories error:\", error);\r\n    throw new Error(\"Unable to fetch categories. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get hierarchical categories structure\r\n * @returns Hierarchical list of categories with subcategories\r\n * @endpoint GET /api/Category/hierarchical\r\n */\r\nexport async function getHierarchicalCategories(): Promise<CategoryDTO[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/hierarchical`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch hierarchical categories\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Get hierarchical categories error:\", error);\r\n    throw new Error(\"Unable to fetch hierarchical categories. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get category by ID\r\n * @param id Category ID\r\n * @returns Category details\r\n * @endpoint GET /api/Category/{id}\r\n */\r\nexport async function getCategoryById(id: number): Promise<Category> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/${id}`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch category\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`Get category by id ${id} error:`, error);\r\n    throw new Error(\"Unable to fetch category. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Create a new category\r\n * @param category Category data\r\n * @returns Created category\r\n * @endpoint POST /api/Category\r\n */\r\nexport async function createCategory(category: CreateCategoryPayload): Promise<Category> {\r\n  try {\r\n    const categoryData = {\r\n      name: category.name,\r\n      slug: category.slug,\r\n      description: category.description,\r\n      parentId: category.parentId,\r\n      isActive: category.isActive ?? true,\r\n      displayOrder: category.displayOrder ?? 0,\r\n      imageId: category.imageId,\r\n    };\r\n\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(categoryData),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to create category\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Create category error:\", error);\r\n    throw new Error(\"Unable to create category. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Update an existing category\r\n * @param category Category data with ID\r\n * @returns void\r\n * @endpoint PUT /api/Category/{id}\r\n */\r\nexport async function updateCategory(category: UpdateCategoryPayload): Promise<void> {\r\n  try {\r\n    const categoryData: any = {};\r\n\r\n    if (category.name) {\r\n      categoryData.name = category.name;\r\n    }\r\n    if (category.slug) {\r\n      categoryData.slug = category.slug;\r\n    }\r\n    if (category.description !== undefined) {\r\n      categoryData.description = category.description;\r\n    }\r\n    if (category.parentId !== undefined) {\r\n      categoryData.parentId = category.parentId;\r\n    }\r\n    if (category.isActive !== undefined) {\r\n      categoryData.isActive = category.isActive;\r\n    }\r\n    if (category.displayOrder !== undefined) {\r\n      categoryData.displayOrder = category.displayOrder;\r\n    }\r\n    if (category.imageId !== undefined) {\r\n      categoryData.imageId = category.imageId;\r\n    }\r\n\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/${category.id}`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(categoryData),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to update category\");\r\n    }\r\n  } catch (error) {\r\n    console.error(`Update category error:`, error);\r\n    throw new Error(\"Unable to update category. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Delete a category\r\n * @param id Category ID\r\n * @returns void\r\n * @endpoint DELETE /api/Category/{id}\r\n */\r\nexport async function deleteCategory(id: number): Promise<void> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to delete category\");\r\n    }\r\n  } catch (error) {\r\n    console.error(`Delete category by id ${id} error:`, error);\r\n    throw new Error(\"Unable to delete category. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get products by category ID\r\n * @param categoryId Category ID\r\n * @returns List of products in the category\r\n * @endpoint GET /api/Category/{id}/Products\r\n */\r\nexport async function getProductsByCategory(categoryId: number): Promise<Product[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/${categoryId}/Products`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch products for this category\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`Get products by category ${categoryId} error:`, error);\r\n    throw new Error(\"Unable to fetch products for this category. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get category by slug\r\n * @param slug Category slug\r\n * @returns Category details\r\n * @endpoint GET /api/Category/slug/{slug}\r\n */\r\nexport async function getCategoryBySlug(slug: string): Promise<Category> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/slug/${encodeURIComponent(slug)}`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch category\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`Get category by slug ${slug} error:`, error);\r\n    throw new Error(\"Unable to fetch category. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get categories with product count\r\n * @returns List of categories with product counts\r\n * @endpoint GET /api/Category/with-product-count\r\n */\r\nexport async function getCategoriesWithProductCount(): Promise<CategoryDTO[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/with-product-count`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch categories with product count\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Get categories with product count error:\", error);\r\n    throw new Error(\"Unable to fetch categories with product count. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get subcategories\r\n * @param parentId Parent category ID\r\n * @returns List of subcategories\r\n * @endpoint GET /api/Category/{parentId}/subcategories\r\n */\r\nexport async function getSubcategories(parentId: number): Promise<CategoryDTO[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/${parentId}/subcategories`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch subcategories\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`Get subcategories for parent ${parentId} error:`, error);\r\n    throw new Error(\"Unable to fetch subcategories. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get category product count\r\n * @param categoryId Category ID\r\n * @returns Product count for the category\r\n * @endpoint GET /api/Category/{categoryId}/product-count\r\n */\r\nexport async function getCategoryProductCount(categoryId: number): Promise<number> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/${categoryId}/product-count`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch category product count\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`Get category product count for ${categoryId} error:`, error);\r\n    throw new Error(\"Unable to fetch category product count. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get popular categories\r\n * @returns List of popular categories\r\n * @endpoint GET /api/Category/popular\r\n */\r\nexport async function getPopularCategories(): Promise<CategoryDTO[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/popular`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch popular categories\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Get popular categories error:\", error);\r\n    throw new Error(\"Unable to fetch popular categories. Please try again later.\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get root categories\r\n * @returns List of root categories\r\n * @endpoint GET /api/Category/root\r\n */\r\nexport async function getRootCategories(): Promise<CategoryDTO[]> {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_URL}/api/Category/root`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Unable to fetch root categories\");\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(\"Get root categories error:\", error);\r\n    throw new Error(\"Unable to fetch root categories. Please try again later.\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA;AACA;AAHA;;;AAwEO,eAAe,cAAc,OAAyB;IAC3D,IAAI;QACF,MAAM,iBAAiB,UAAU,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,CAAC;QAC1D,MAAM,MAAM,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;QAErC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,IAAI,MAAM;IAClB;AACF;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;QAElE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,0BAA0B,CAAC;QAE3E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,gBAAgB,EAAU;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,cAAc,EAAE,IAAI;QAEpE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC,EAAE;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,eAAe,QAA+B;IAClE,IAAI;QACF,MAAM,eAAe;YACnB,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ,IAAI;YAC/B,cAAc,SAAS,YAAY,IAAI;YACvC,SAAS,SAAS,OAAO;QAC3B;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,eAAe,QAA+B;IAClE,IAAI;QACF,MAAM,eAAoB,CAAC;QAE3B,IAAI,SAAS,IAAI,EAAE;YACjB,aAAa,IAAI,GAAG,SAAS,IAAI;QACnC;QACA,IAAI,SAAS,IAAI,EAAE;YACjB,aAAa,IAAI,GAAG,SAAS,IAAI;QACnC;QACA,IAAI,SAAS,WAAW,KAAK,WAAW;YACtC,aAAa,WAAW,GAAG,SAAS,WAAW;QACjD;QACA,IAAI,SAAS,QAAQ,KAAK,WAAW;YACnC,aAAa,QAAQ,GAAG,SAAS,QAAQ;QAC3C;QACA,IAAI,SAAS,QAAQ,KAAK,WAAW;YACnC,aAAa,QAAQ,GAAG,SAAS,QAAQ;QAC3C;QACA,IAAI,SAAS,YAAY,KAAK,WAAW;YACvC,aAAa,YAAY,GAAG,SAAS,YAAY;QACnD;QACA,IAAI,SAAS,OAAO,KAAK,WAAW;YAClC,aAAa,OAAO,GAAG,SAAS,OAAO;QACzC;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,cAAc,EAAE,SAAS,EAAE,EAAE,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,CAAC,EAAE;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,eAAe,EAAU;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,cAAc,EAAE,IAAI,EAAE;YACpE,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,OAAO,CAAC,EAAE;QACpD,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,sBAAsB,UAAkB;IAC5D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,cAAc,EAAE,WAAW,SAAS,CAAC;QAErF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,WAAW,OAAO,CAAC,EAAE;QAC/D,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,kBAAkB,IAAY;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,mBAAmB,OAAO;QAE/F,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,OAAO,CAAC,EAAE;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,gCAAgC,CAAC;QAEjF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,iBAAiB,QAAgB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,cAAc,EAAE,SAAS,cAAc,CAAC;QAExF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,SAAS,OAAO,CAAC,EAAE;QACjE,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,wBAAwB,UAAkB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,cAAc,EAAE,WAAW,cAAc,CAAC;QAE1F,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,WAAW,OAAO,CAAC,EAAE;QACrE,MAAM,IAAI,MAAM;IAClB;AACF;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,qBAAqB,CAAC;QAEtE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM;IAClB;AACF;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,sHAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC;QAEnE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/stores/categoryStore.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { devtools } from \"zustand/middleware\";\r\nimport { CategoryDTO } from \"@/services/categories\";\r\nimport { getHierarchicalCategories } from \"@/services/categories\";\r\n\r\n/**\r\n * Category store state interface\r\n */\r\ninterface CategoryState {\r\n  // Data\r\n  categories: CategoryDTO[];\r\n  flatCategories: CategoryDTO[];\r\n  \r\n  // Loading states\r\n  isLoading: boolean;\r\n  isInitialized: boolean;\r\n  \r\n  // Error state\r\n  error: string | null;\r\n  \r\n  // Actions\r\n  initializeCategories: () => Promise<void>;\r\n  addCategory: (category: CategoryDTO) => void;\r\n  updateCategory: (category: CategoryDTO) => void;\r\n  removeCategory: (categoryId: number) => void;\r\n  clearError: () => void;\r\n  reset: () => void;\r\n  \r\n  // Selectors\r\n  getCategoryById: (id: number) => CategoryDTO | undefined;\r\n  getRootCategories: () => CategoryDTO[];\r\n  getSubcategories: (parentId: number) => CategoryDTO[];\r\n  getCategoryPath: (categoryId: number) => CategoryDTO[];\r\n}\r\n\r\n/**\r\n * Utility function to flatten hierarchical categories\r\n */\r\nconst flattenCategories = (categories: CategoryDTO[]): CategoryDTO[] => {\r\n  const flattened: CategoryDTO[] = [];\r\n  \r\n  const flatten = (cats: CategoryDTO[]) => {\r\n    cats.forEach(category => {\r\n      flattened.push(category);\r\n      if (category.subcategories && category.subcategories.length > 0) {\r\n        flatten(category.subcategories);\r\n      }\r\n    });\r\n  };\r\n  \r\n  flatten(categories);\r\n  return flattened;\r\n};\r\n\r\n/**\r\n * Utility function to find category by ID in hierarchical structure\r\n */\r\nconst findCategoryById = (categories: CategoryDTO[], id: number): CategoryDTO | undefined => {\r\n  for (const category of categories) {\r\n    if (category.id === id) {\r\n      return category;\r\n    }\r\n    if (category.subcategories && category.subcategories.length > 0) {\r\n      const found = findCategoryById(category.subcategories, id);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return undefined;\r\n};\r\n\r\n/**\r\n * Utility function to update category in hierarchical structure\r\n */\r\nconst updateCategoryInHierarchy = (categories: CategoryDTO[], updatedCategory: CategoryDTO): CategoryDTO[] => {\r\n  return categories.map(category => {\r\n    if (category.id === updatedCategory.id) {\r\n      return { ...updatedCategory, subcategories: category.subcategories };\r\n    }\r\n    if (category.subcategories && category.subcategories.length > 0) {\r\n      return {\r\n        ...category,\r\n        subcategories: updateCategoryInHierarchy(category.subcategories, updatedCategory)\r\n      };\r\n    }\r\n    return category;\r\n  });\r\n};\r\n\r\n/**\r\n * Utility function to remove category from hierarchical structure\r\n */\r\nconst removeCategoryFromHierarchy = (categories: CategoryDTO[], categoryId: number): CategoryDTO[] => {\r\n  return categories\r\n    .filter(category => category.id !== categoryId)\r\n    .map(category => ({\r\n      ...category,\r\n      subcategories: category.subcategories \r\n        ? removeCategoryFromHierarchy(category.subcategories, categoryId)\r\n        : undefined\r\n    }));\r\n};\r\n\r\n/**\r\n * Utility function to add category to hierarchical structure\r\n */\r\nconst addCategoryToHierarchy = (categories: CategoryDTO[], newCategory: CategoryDTO): CategoryDTO[] => {\r\n  // If it's a root category (no parentId)\r\n  if (!newCategory.parentId) {\r\n    return [...categories, newCategory];\r\n  }\r\n  \r\n  // Find parent and add as subcategory\r\n  return categories.map(category => {\r\n    if (category.id === newCategory.parentId) {\r\n      return {\r\n        ...category,\r\n        subcategories: [...(category.subcategories || []), newCategory]\r\n      };\r\n    }\r\n    if (category.subcategories && category.subcategories.length > 0) {\r\n      return {\r\n        ...category,\r\n        subcategories: addCategoryToHierarchy(category.subcategories, newCategory)\r\n      };\r\n    }\r\n    return category;\r\n  });\r\n};\r\n\r\n/**\r\n * Category store implementation using Zustand\r\n */\r\nexport const useCategoryStore = create<CategoryState>()(\r\n  devtools(\r\n    (set, get) => ({\r\n      // Initial state\r\n      categories: [],\r\n      flatCategories: [],\r\n      isLoading: false,\r\n      isInitialized: false,\r\n      error: null,\r\n\r\n      // Initialize categories from API\r\n      initializeCategories: async () => {\r\n        const { isInitialized, isLoading } = get();\r\n        \r\n        // Prevent multiple initializations\r\n        if (isInitialized || isLoading) {\r\n          return;\r\n        }\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        try {\r\n          const hierarchicalCategories = await getHierarchicalCategories();\r\n          const flatCategories = flattenCategories(hierarchicalCategories);\r\n\r\n          set({\r\n            categories: hierarchicalCategories,\r\n            flatCategories,\r\n            isLoading: false,\r\n            isInitialized: true,\r\n            error: null,\r\n          });\r\n        } catch (error) {\r\n          console.error(\"Failed to initialize categories:\", error);\r\n          set({\r\n            isLoading: false,\r\n            error: error instanceof Error ? error.message : \"Failed to load categories\",\r\n          });\r\n        }\r\n      },\r\n\r\n      // Add new category\r\n      addCategory: (category: CategoryDTO) => {\r\n        const { categories } = get();\r\n        const updatedCategories = addCategoryToHierarchy(categories, category);\r\n        const flatCategories = flattenCategories(updatedCategories);\r\n\r\n        set({\r\n          categories: updatedCategories,\r\n          flatCategories,\r\n        });\r\n      },\r\n\r\n      // Update existing category\r\n      updateCategory: (category: CategoryDTO) => {\r\n        const { categories } = get();\r\n        const updatedCategories = updateCategoryInHierarchy(categories, category);\r\n        const flatCategories = flattenCategories(updatedCategories);\r\n\r\n        set({\r\n          categories: updatedCategories,\r\n          flatCategories,\r\n        });\r\n      },\r\n\r\n      // Remove category\r\n      removeCategory: (categoryId: number) => {\r\n        const { categories } = get();\r\n        const updatedCategories = removeCategoryFromHierarchy(categories, categoryId);\r\n        const flatCategories = flattenCategories(updatedCategories);\r\n\r\n        set({\r\n          categories: updatedCategories,\r\n          flatCategories,\r\n        });\r\n      },\r\n\r\n      // Clear error\r\n      clearError: () => {\r\n        set({ error: null });\r\n      },\r\n\r\n      // Reset store\r\n      reset: () => {\r\n        set({\r\n          categories: [],\r\n          flatCategories: [],\r\n          isLoading: false,\r\n          isInitialized: false,\r\n          error: null,\r\n        });\r\n      },\r\n\r\n      // Selectors\r\n      getCategoryById: (id: number) => {\r\n        const { flatCategories } = get();\r\n        return flatCategories.find(category => category.id === id);\r\n      },\r\n\r\n      getRootCategories: () => {\r\n        const { categories } = get();\r\n        return categories;\r\n      },\r\n\r\n      getSubcategories: (parentId: number) => {\r\n        const { categories } = get();\r\n        const parent = findCategoryById(categories, parentId);\r\n        return parent?.subcategories || [];\r\n      },\r\n\r\n      getCategoryPath: (categoryId: number) => {\r\n        const { flatCategories } = get();\r\n        const path: CategoryDTO[] = [];\r\n        \r\n        const findPath = (id: number): boolean => {\r\n          const category = flatCategories.find(cat => cat.id === id);\r\n          if (!category) return false;\r\n          \r\n          path.unshift(category);\r\n          \r\n          if (category.parentId) {\r\n            return findPath(category.parentId);\r\n          }\r\n          \r\n          return true;\r\n        };\r\n        \r\n        findPath(categoryId);\r\n        return path;\r\n      },\r\n    }),\r\n    {\r\n      name: \"category-store\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AALA;;;;AAqCA;;CAEC,GACD,MAAM,oBAAoB,CAAC;IACzB,MAAM,YAA2B,EAAE;IAEnC,MAAM,UAAU,CAAC;QACf,KAAK,OAAO,CAAC,CAAA;YACX,UAAU,IAAI,CAAC;YACf,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;gBAC/D,QAAQ,SAAS,aAAa;YAChC;QACF;IACF;IAEA,QAAQ;IACR,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,mBAAmB,CAAC,YAA2B;IACnD,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,EAAE,KAAK,IAAI;YACtB,OAAO;QACT;QACA,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;YAC/D,MAAM,QAAQ,iBAAiB,SAAS,aAAa,EAAE;YACvD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,4BAA4B,CAAC,YAA2B;IAC5D,OAAO,WAAW,GAAG,CAAC,CAAA;QACpB,IAAI,SAAS,EAAE,KAAK,gBAAgB,EAAE,EAAE;YACtC,OAAO;gBAAE,GAAG,eAAe;gBAAE,eAAe,SAAS,aAAa;YAAC;QACrE;QACA,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;YAC/D,OAAO;gBACL,GAAG,QAAQ;gBACX,eAAe,0BAA0B,SAAS,aAAa,EAAE;YACnE;QACF;QACA,OAAO;IACT;AACF;AAEA;;CAEC,GACD,MAAM,8BAA8B,CAAC,YAA2B;IAC9D,OAAO,WACJ,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,YACnC,GAAG,CAAC,CAAA,WAAY,CAAC;YAChB,GAAG,QAAQ;YACX,eAAe,SAAS,aAAa,GACjC,4BAA4B,SAAS,aAAa,EAAE,cACpD;QACN,CAAC;AACL;AAEA;;CAEC,GACD,MAAM,yBAAyB,CAAC,YAA2B;IACzD,wCAAwC;IACxC,IAAI,CAAC,YAAY,QAAQ,EAAE;QACzB,OAAO;eAAI;YAAY;SAAY;IACrC;IAEA,qCAAqC;IACrC,OAAO,WAAW,GAAG,CAAC,CAAA;QACpB,IAAI,SAAS,EAAE,KAAK,YAAY,QAAQ,EAAE;YACxC,OAAO;gBACL,GAAG,QAAQ;gBACX,eAAe;uBAAK,SAAS,aAAa,IAAI,EAAE;oBAAG;iBAAY;YACjE;QACF;QACA,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;YAC/D,OAAO;gBACL,GAAG,QAAQ;gBACX,eAAe,uBAAuB,SAAS,aAAa,EAAE;YAChE;QACF;QACA,OAAO;IACT;AACF;AAKO,MAAM,mBAAmB,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,IACnC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,YAAY,EAAE;QACd,gBAAgB,EAAE;QAClB,WAAW;QACX,eAAe;QACf,OAAO;QAEP,iCAAiC;QACjC,sBAAsB;YACpB,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG;YAErC,mCAAmC;YACnC,IAAI,iBAAiB,WAAW;gBAC9B;YACF;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD;gBAC7D,MAAM,iBAAiB,kBAAkB;gBAEzC,IAAI;oBACF,YAAY;oBACZ;oBACA,WAAW;oBACX,eAAe;oBACf,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;QACF;QAEA,mBAAmB;QACnB,aAAa,CAAC;YACZ,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,oBAAoB,uBAAuB,YAAY;YAC7D,MAAM,iBAAiB,kBAAkB;YAEzC,IAAI;gBACF,YAAY;gBACZ;YACF;QACF;QAEA,2BAA2B;QAC3B,gBAAgB,CAAC;YACf,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,oBAAoB,0BAA0B,YAAY;YAChE,MAAM,iBAAiB,kBAAkB;YAEzC,IAAI;gBACF,YAAY;gBACZ;YACF;QACF;QAEA,kBAAkB;QAClB,gBAAgB,CAAC;YACf,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,oBAAoB,4BAA4B,YAAY;YAClE,MAAM,iBAAiB,kBAAkB;YAEzC,IAAI;gBACF,YAAY;gBACZ;YACF;QACF;QAEA,cAAc;QACd,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,cAAc;QACd,OAAO;YACL,IAAI;gBACF,YAAY,EAAE;gBACd,gBAAgB,EAAE;gBAClB,WAAW;gBACX,eAAe;gBACf,OAAO;YACT;QACF;QAEA,YAAY;QACZ,iBAAiB,CAAC;YAChB,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,OAAO,eAAe,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;QACzD;QAEA,mBAAmB;YACjB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,OAAO;QACT;QAEA,kBAAkB,CAAC;YACjB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,SAAS,iBAAiB,YAAY;YAC5C,OAAO,QAAQ,iBAAiB,EAAE;QACpC;QAEA,iBAAiB,CAAC;YAChB,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,OAAsB,EAAE;YAE9B,MAAM,WAAW,CAAC;gBAChB,MAAM,WAAW,eAAe,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBACvD,IAAI,CAAC,UAAU,OAAO;gBAEtB,KAAK,OAAO,CAAC;gBAEb,IAAI,SAAS,QAAQ,EAAE;oBACrB,OAAO,SAAS,SAAS,QAAQ;gBACnC;gBAEA,OAAO;YACT;YAEA,SAAS;YACT,OAAO;QACT;IACF,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/providers/CategoryStoreProvider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useCategoryStore } from \"@/stores/categoryStore\";\r\n\r\n/**\r\n * Category Store Provider Component\r\n *\r\n * This component initializes the category store when the user is authenticated\r\n * and resets it when the user logs out.\r\n */\r\nexport function CategoryStoreProvider({ children }: { children: React.ReactNode }) {\r\n  const { data: session, status } = useSession();\r\n  const pathname = usePathname();\r\n  const { initializeCategories, reset, isInitialized } = useCategoryStore();\r\n\r\n  useEffect(() => {\r\n    // Don't initialize categories on login page or other public pages\r\n    if (pathname === \"/login\" || pathname === \"/\") {\r\n      return;\r\n    }\r\n\r\n    // Initialize categories when user is authenticated\r\n    if (status === \"authenticated\" && session?.user && !isInitialized) {\r\n      console.log(\"Initializing category store for authenticated user\");\r\n      initializeCategories();\r\n    }\r\n\r\n    // Reset store when user logs out (but not on login page)\r\n    if (status === \"unauthenticated\" && pathname !== \"/login\") {\r\n      console.log(\"Resetting category store for unauthenticated user\");\r\n      reset();\r\n    }\r\n  }, [status, session, pathname, isInitialized, initializeCategories, reset]);\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaO,SAAS,sBAAsB,EAAE,QAAQ,EAAiC;;IAC/E,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,oBAAoB,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,kEAAkE;YAClE,IAAI,aAAa,YAAY,aAAa,KAAK;gBAC7C;YACF;YAEA,mDAAmD;YACnD,IAAI,WAAW,mBAAmB,SAAS,QAAQ,CAAC,eAAe;gBACjE,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,yDAAyD;YACzD,IAAI,WAAW,qBAAqB,aAAa,UAAU;gBACzD,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;0CAAG;QAAC;QAAQ;QAAS;QAAU;QAAe;QAAsB;KAAM;IAE1E,qBAAO;kBAAG;;AACZ;GAzBgB;;QACoB,iJAAA,CAAA,aAAU;QAC3B,qIAAA,CAAA,cAAW;QAC2B,0HAAA,CAAA,mBAAgB;;;KAHzD", "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/services/fileManager.ts"], "sourcesContent": ["/**\r\n * File Manager API Service\r\n */\r\n\r\nimport { API_URL, fetchWithAuth, fetchWithAuthFormData } from \"@/lib/api-utils\";\r\nimport {\r\n  BrowseResponse,\r\n  BrowseParams,\r\n  FolderStructure,\r\n  FileItem,\r\n  UploadResponse,\r\n  CreateFolderRequest,\r\n  DeleteRequest,\r\n  DeleteResponse,\r\n  MoveRequest,\r\n  MoveResponse,\r\n  ImageMetadata,\r\n} from \"@/types/fileManager\";\r\n\r\nconst BASE_URL = `${API_URL}/api/filemanager`;\r\n\r\nexport const fileManagerService = {\r\n  /**\r\n   * Health check\r\n   */\r\n  async healthCheck(): Promise<{ status: string; uploadsPath: string }> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/health`);\r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to check health\");\r\n    }\r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Browse files and folders\r\n   */\r\n  async browse(params: BrowseParams = {}): Promise<BrowseResponse> {\r\n    const url = `${BASE_URL}/browse`;\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      body: JSON.stringify({\r\n        path: params.path || \"\",\r\n        search: params.search || \"\",\r\n        fileType: params.fileType || \"all\",\r\n        extension: params.extension || \"\",\r\n        sortBy: params.sortBy || \"name\",\r\n        sortOrder: params.sortOrder || \"asc\",\r\n        minSize: params.minSize,\r\n        maxSize: params.maxSize,\r\n        fromDate: params.fromDate,\r\n        toDate: params.toDate,\r\n        pageNumber: params.page || 1,\r\n        pageSize: params.pageSize || 20,\r\n      }),\r\n    });\r\n    if (!response.ok) throw new Error(\"Failed to browse files\");\r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Get folder structure\r\n   */\r\n  async getFolderStructure(rootPath: string = \"\"): Promise<FolderStructure> {\r\n    const searchParams = new URLSearchParams();\r\n    if (rootPath) searchParams.append(\"rootPath\", rootPath);\r\n\r\n    const url = `${BASE_URL}/folder-structure${searchParams.toString() ? `?${searchParams.toString()}` : \"\"}`;\r\n    const response = await fetchWithAuth(url);\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to get folder structure\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Get file info\r\n   */\r\n  async getFileInfo(filePath: string): Promise<FileItem> {\r\n    const searchParams = new URLSearchParams();\r\n    searchParams.append(\"filePath\", filePath);\r\n\r\n    const response = await fetchWithAuth(`${BASE_URL}/info?${searchParams.toString()}`);\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to get file info\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Upload files\r\n   */\r\n  async uploadFiles(\r\n    files: File[],\r\n    folderPath: string = \"\",\r\n    createThumbnails: boolean = true,\r\n    overwriteExisting: boolean = false\r\n  ): Promise<UploadResponse> {\r\n    const formData = new FormData();\r\n    \r\n    files.forEach(file => {\r\n      formData.append(\"files\", file);\r\n    });\r\n    \r\n    formData.append(\"FolderPath\", folderPath);\r\n    formData.append(\"CreateThumbnails\", createThumbnails.toString());\r\n    formData.append(\"OverwriteExisting\", overwriteExisting.toString());\r\n\r\n    const response = await fetchWithAuthFormData(`${BASE_URL}/upload`, formData, \"POST\");\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to upload files\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Create folder\r\n   */\r\n  async createFolder(request: CreateFolderRequest): Promise<FileItem> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/create-folder`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(request),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to create folder\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Delete files/folders\r\n   */\r\n  async deleteFiles(request: DeleteRequest): Promise<DeleteResponse> {\r\n    console.log(\"Deleting files with request:\", request);\r\n    \r\n    const response = await fetchWithAuth(`${BASE_URL}/delete`, {\r\n      method: \"DELETE\",\r\n      body: JSON.stringify(request),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to delete files\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Move files\r\n   */\r\n  async moveFiles(request: MoveRequest): Promise<MoveResponse> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/move`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(request),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to move files\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Copy files\r\n   */\r\n  async copyFiles(request: MoveRequest): Promise<MoveResponse> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/copy`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(request),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to copy files\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Generate thumbnail\r\n   */\r\n  async generateThumbnail(imagePath: string): Promise<{ thumbnailUrl: string }> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/generate-thumbnail`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(imagePath),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to generate thumbnail\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Get image metadata\r\n   */\r\n  async getImageMetadata(imagePath: string): Promise<ImageMetadata> {\r\n    const searchParams = new URLSearchParams();\r\n    searchParams.append(\"imagePath\", imagePath);\r\n\r\n    const response = await fetchWithAuth(`${BASE_URL}/metadata?${searchParams.toString()}`);\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to get image metadata\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Cleanup orphaned files\r\n   */\r\n  async cleanupOrphanedFiles(): Promise<{ cleanedCount: number; message: string }> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/cleanup-orphaned`, {\r\n      method: \"POST\",\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to cleanup orphaned files\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Sync database\r\n   */\r\n  async syncDatabase(): Promise<{ syncedCount: number; message: string }> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/sync-database`, {\r\n      method: \"POST\",\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to sync database\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Get missing files\r\n   */\r\n  async getMissingFiles(): Promise<{ missingFiles: string[]; count: number }> {\r\n    const response = await fetchWithAuth(`${BASE_URL}/missing-files`);\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to get missing files\");\r\n    }\r\n    \r\n    return response.json();\r\n  },\r\n\r\n  /**\r\n   * Get download URL for a file\r\n   */\r\n  getDownloadUrl(relativePath: string): string {\r\n    // API_URL is imported from @/lib/api-utils\r\n    // API endpoint uses filePath parameter as per documentation\r\n    return `${BASE_URL}/download?filePath=${encodeURIComponent(relativePath)}`;\r\n  },\r\n};\r\n\r\n/**\r\n * Utility functions\r\n */\r\nexport const fileManagerUtils = {\r\n  /**\r\n   * Get file icon based on extension\r\n   */\r\n  getFileIcon(extension: string): string {\r\n    const ext = extension.toLowerCase();\r\n    \r\n    if ([\".jpg\", \".jpeg\", \".png\", \".gif\", \".webp\", \".bmp\"].includes(ext)) {\r\n      return \"image\";\r\n    }\r\n    if ([\".pdf\"].includes(ext)) {\r\n      return \"file-text\";\r\n    }\r\n    if ([\".doc\", \".docx\"].includes(ext)) {\r\n      return \"file-text\";\r\n    }\r\n    if ([\".xls\", \".xlsx\"].includes(ext)) {\r\n      return \"file-spreadsheet\";\r\n    }\r\n    if ([\".ppt\", \".pptx\"].includes(ext)) {\r\n      return \"presentation\";\r\n    }\r\n    if ([\".mp4\", \".avi\", \".mov\", \".wmv\"].includes(ext)) {\r\n      return \"video\";\r\n    }\r\n    if ([\".mp3\", \".wav\", \".flac\", \".aac\"].includes(ext)) {\r\n      return \"music\";\r\n    }\r\n    if ([\".zip\", \".rar\", \".7z\"].includes(ext)) {\r\n      return \"archive\";\r\n    }\r\n    \r\n    return \"file\";\r\n  },\r\n\r\n  /**\r\n   * Format file size\r\n   */\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes === 0) return \"0 B\";\r\n    \r\n    const k = 1024;\r\n    const sizes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\"];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    \r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\r\n  },\r\n\r\n  /**\r\n   * Get breadcrumb from path\r\n   */\r\n  getBreadcrumb(path: string): Array<{ name: string; path: string }> {\r\n    if (!path) return [{ name: \"My Drive\", path: \"\" }];\r\n    \r\n    const parts = path.split(\"/\").filter(Boolean);\r\n    const breadcrumb = [{ name: \"My Drive\", path: \"\" }];\r\n    \r\n    let currentPath = \"\";\r\n    parts.forEach(part => {\r\n      currentPath += (currentPath ? \"/\" : \"\") + part;\r\n      breadcrumb.push({ name: part, path: currentPath });\r\n    });\r\n    \r\n    return breadcrumb;\r\n  },\r\n\r\n  /**\r\n   * Check if file is image\r\n   */\r\n  isImage(extension: string): boolean {\r\n    const imageExtensions = [\".jpg\", \".jpeg\", \".png\", \".gif\", \".webp\", \".bmp\"];\r\n    return imageExtensions.includes(extension.toLowerCase());\r\n  },\r\n\r\n  /**\r\n   * Get file type color\r\n   */\r\n  getFileTypeColor(type: string, extension: string): string {\r\n    if (type === \"folder\") return \"text-blue-500\";\r\n    if (type === \"image\") return \"text-green-500\";\r\n    \r\n    const ext = extension.toLowerCase();\r\n    if ([\".pdf\"].includes(ext)) return \"text-red-500\";\r\n    if ([\".doc\", \".docx\"].includes(ext)) return \"text-blue-600\";\r\n    if ([\".xls\", \".xlsx\"].includes(ext)) return \"text-green-600\";\r\n    if ([\".ppt\", \".pptx\"].includes(ext)) return \"text-orange-500\";\r\n    if ([\".mp4\", \".avi\", \".mov\"].includes(ext)) return \"text-purple-500\";\r\n    if ([\".mp3\", \".wav\", \".flac\"].includes(ext)) return \"text-pink-500\";\r\n    \r\n    return \"text-gray-500\";\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;;AAeA,MAAM,WAAW,GAAG,sHAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;AAEtC,MAAM,qBAAqB;IAChC;;GAEC,GACD,MAAM;QACJ,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,OAAO,CAAC;QACzD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,QAAO,SAAuB,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,SAAS,OAAO,CAAC;QAChC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YACxC,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBACnB,MAAM,OAAO,IAAI,IAAI;gBACrB,QAAQ,OAAO,MAAM,IAAI;gBACzB,UAAU,OAAO,QAAQ,IAAI;gBAC7B,WAAW,OAAO,SAAS,IAAI;gBAC/B,QAAQ,OAAO,MAAM,IAAI;gBACzB,WAAW,OAAO,SAAS,IAAI;gBAC/B,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;gBACrB,YAAY,OAAO,IAAI,IAAI;gBAC3B,UAAU,OAAO,QAAQ,IAAI;YAC/B;QACF;QACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAClC,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,oBAAmB,WAAmB,EAAE;QAC5C,MAAM,eAAe,IAAI;QACzB,IAAI,UAAU,aAAa,MAAM,CAAC,YAAY;QAE9C,MAAM,MAAM,GAAG,SAAS,iBAAiB,EAAE,aAAa,QAAQ,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI,GAAG,IAAI;QACzG,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;QAErC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,aAAY,QAAgB;QAChC,MAAM,eAAe,IAAI;QACzB,aAAa,MAAM,CAAC,YAAY;QAEhC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,MAAM,EAAE,aAAa,QAAQ,IAAI;QAElF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,aACJ,KAAa,EACb,aAAqB,EAAE,EACvB,mBAA4B,IAAI,EAChC,oBAA6B,KAAK;QAElC,MAAM,WAAW,IAAI;QAErB,MAAM,OAAO,CAAC,CAAA;YACZ,SAAS,MAAM,CAAC,SAAS;QAC3B;QAEA,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,oBAAoB,iBAAiB,QAAQ;QAC7D,SAAS,MAAM,CAAC,qBAAqB,kBAAkB,QAAQ;QAE/D,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG,SAAS,OAAO,CAAC,EAAE,UAAU;QAE7E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,cAAa,OAA4B;QAC7C,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,cAAc,CAAC,EAAE;YAChE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,aAAY,OAAsB;QACtC,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,OAAO,CAAC,EAAE;YACzD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,WAAU,OAAoB;QAClC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE;YACvD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,WAAU,OAAoB;QAClC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE;YACvD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,mBAAkB,SAAiB;QACvC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,mBAAmB,CAAC,EAAE;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,kBAAiB,SAAiB;QACtC,MAAM,eAAe,IAAI;QACzB,aAAa,MAAM,CAAC,aAAa;QAEjC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,UAAU,EAAE,aAAa,QAAQ,IAAI;QAEtF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,iBAAiB,CAAC,EAAE;YACnE,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,cAAc,CAAC,EAAE;YAChE,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,cAAc,CAAC;QAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,gBAAe,YAAoB;QACjC,2CAA2C;QAC3C,4DAA4D;QAC5D,OAAO,GAAG,SAAS,mBAAmB,EAAE,mBAAmB,eAAe;IAC5E;AACF;AAKO,MAAM,mBAAmB;IAC9B;;GAEC,GACD,aAAY,SAAiB;QAC3B,MAAM,MAAM,UAAU,WAAW;QAEjC,IAAI;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAS;SAAO,CAAC,QAAQ,CAAC,MAAM;YACpE,OAAO;QACT;QACA,IAAI;YAAC;SAAO,CAAC,QAAQ,CAAC,MAAM;YAC1B,OAAO;QACT;QACA,IAAI;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO;QACT;QACA,IAAI;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO;QACT;QACA,IAAI;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO;QACT;QACA,IAAI;YAAC;YAAQ;YAAQ;YAAQ;SAAO,CAAC,QAAQ,CAAC,MAAM;YAClD,OAAO;QACT;QACA,IAAI;YAAC;YAAQ;YAAQ;YAAS;SAAO,CAAC,QAAQ,CAAC,MAAM;YACnD,OAAO;QACT;QACA,IAAI;YAAC;YAAQ;YAAQ;SAAM,CAAC,QAAQ,CAAC,MAAM;YACzC,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,gBAAe,KAAa;QAC1B,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;YAAM;SAAK;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA;;GAEC,GACD,eAAc,IAAY;QACxB,IAAI,CAAC,MAAM,OAAO;YAAC;gBAAE,MAAM;gBAAY,MAAM;YAAG;SAAE;QAElD,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC;QACrC,MAAM,aAAa;YAAC;gBAAE,MAAM;gBAAY,MAAM;YAAG;SAAE;QAEnD,IAAI,cAAc;QAClB,MAAM,OAAO,CAAC,CAAA;YACZ,eAAe,CAAC,cAAc,MAAM,EAAE,IAAI;YAC1C,WAAW,IAAI,CAAC;gBAAE,MAAM;gBAAM,MAAM;YAAY;QAClD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,SAAQ,SAAiB;QACvB,MAAM,kBAAkB;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAS;SAAO;QAC1E,OAAO,gBAAgB,QAAQ,CAAC,UAAU,WAAW;IACvD;IAEA;;GAEC,GACD,kBAAiB,IAAY,EAAE,SAAiB;QAC9C,IAAI,SAAS,UAAU,OAAO;QAC9B,IAAI,SAAS,SAAS,OAAO;QAE7B,MAAM,MAAM,UAAU,WAAW;QACjC,IAAI;YAAC;SAAO,CAAC,QAAQ,CAAC,MAAM,OAAO;QACnC,IAAI;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC5C,IAAI;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC5C,IAAI;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC5C,IAAI;YAAC;YAAQ;YAAQ;SAAO,CAAC,QAAQ,CAAC,MAAM,OAAO;QACnD,IAAI;YAAC;YAAQ;YAAQ;SAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;QAEpD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\n// Inspired by react-hot-toast library\r\nimport type * as React from \"react\"\r\nimport { useToast as useToastInternal } from \"@/providers/ToastProvider\"\r\nimport type { ToastProps } from \"@/providers/ToastProvider\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: React.ReactNode\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t,\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction useToast() {\r\n  const { addToast, removeToast, updateToast } = useToastInternal()\r\n\r\n  return {\r\n    success: (props: Omit<ToastProps, \"id\" | \"type\">) => addToast({ ...props, type: \"success\" }),\r\n\r\n    error: (props: Omit<ToastProps, \"id\" | \"type\">) => addToast({ ...props, type: \"error\" }),\r\n\r\n    warning: (props: Omit<ToastProps, \"id\" | \"type\">) => addToast({ ...props, type: \"warning\" }),\r\n\r\n    info: (props: Omit<ToastProps, \"id\" | \"type\">) => addToast({ ...props, type: \"info\" }),\r\n\r\n    custom: (props: Omit<ToastProps, \"id\">) => addToast(props),\r\n\r\n    dismiss: (id: string) => removeToast(id),\r\n\r\n    update: (id: string, props: Partial<ToastProps>) => updateToast(id, props),\r\n\r\n    promise: async <T,>(\r\n      promise: Promise<T>,\r\n      options: {\r\n        loading: Omit<ToastProps, \"id\">\r\n        success: (data: T) => Omit<ToastProps, \"id\">\r\n        error: (err: unknown) => Omit<ToastProps, \"id\">\r\n      },\r\n    ) => {\r\n      const toastId = addToast({ ...options.loading, type: \"info\" })\r\n\r\n      try {\r\n        const data = await promise\r\n        updateToast(toastId, { ...options.success(data), type: \"success\" })\r\n        return data\r\n      } catch (err) {\r\n        updateToast(toastId, { ...options.error(err), type: \"error\" })\r\n        throw err\r\n      }\r\n    },\r\n  }\r\n}\r\n\r\nexport { useToast }\r\n"], "names": [], "mappings": ";;;;AAIA;;AAJA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAC1F;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS;;IACP,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,WAAgB,AAAD;IAE9D,OAAO;QACL,SAAS,CAAC,QAA2C,SAAS;gBAAE,GAAG,KAAK;gBAAE,MAAM;YAAU;QAE1F,OAAO,CAAC,QAA2C,SAAS;gBAAE,GAAG,KAAK;gBAAE,MAAM;YAAQ;QAEtF,SAAS,CAAC,QAA2C,SAAS;gBAAE,GAAG,KAAK;gBAAE,MAAM;YAAU;QAE1F,MAAM,CAAC,QAA2C,SAAS;gBAAE,GAAG,KAAK;gBAAE,MAAM;YAAO;QAEpF,QAAQ,CAAC,QAAkC,SAAS;QAEpD,SAAS,CAAC,KAAe,YAAY;QAErC,QAAQ,CAAC,IAAY,QAA+B,YAAY,IAAI;QAEpE,SAAS,OACP,SACA;YAMA,MAAM,UAAU,SAAS;gBAAE,GAAG,QAAQ,OAAO;gBAAE,MAAM;YAAO;YAE5D,IAAI;gBACF,MAAM,OAAO,MAAM;gBACnB,YAAY,SAAS;oBAAE,GAAG,QAAQ,OAAO,CAAC,KAAK;oBAAE,MAAM;gBAAU;gBACjE,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,YAAY,SAAS;oBAAE,GAAG,QAAQ,KAAK,CAAC,IAAI;oBAAE,MAAM;gBAAQ;gBAC5D,MAAM;YACR;QACF;IACF;AACF;GAtCS;;QACwC,8HAAA,CAAA,WAAgB", "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/contexts/FileManagerContext.tsx"], "sourcesContent": ["/**\r\n * File Manager Context\r\n */\r\n\r\n\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from \"react\";\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { fileManagerService } from \"@/services/fileManager\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { fetchWithAuth } from \"@/lib/api-utils\";\r\nimport {\r\n  BrowseParams,\r\n  FileFilters,\r\n  ViewMode,\r\n  CreateFolderRequest,\r\n  DeleteRequest,\r\n  MoveRequest,\r\n  UploadProgress,\r\n} from \"@/types/fileManager\";\r\n\r\n// Query keys\r\nexport const fileManagerKeys = {\r\n  all: [\"fileManager\"] as const,\r\n  browse: (params: BrowseParams) => [...fileManagerKeys.all, \"browse\", params] as const,\r\n  folders: (rootPath: string) => [...fileManagerKeys.all, \"folders\", rootPath] as const,\r\n  fileInfo: (filePath: string) => [...fileManagerKeys.all, \"fileInfo\", filePath] as const,\r\n};\r\n\r\n// Default filters\r\nconst defaultFilters: FileFilters = {\r\n  search: \"\",\r\n  fileType: \"all\",\r\n  extension: \"\",\r\n  sortBy: \"name\",\r\n  sortOrder: \"asc\",\r\n  dateRange: {},\r\n  sizeRange: {},\r\n};\r\n\r\n// Context type definition\r\ninterface FileManagerContextType {\r\n  // Data\r\n  browseData: any;\r\n  folderStructure: any;\r\n  rootFolderStructure: any;\r\n  \r\n  // State\r\n  currentPath: string;\r\n  selectedItems: string[];\r\n  viewMode: ViewMode;\r\n  filters: FileFilters;\r\n  uploadProgress: UploadProgress[];\r\n  \r\n  // Loading states\r\n  isLoading: boolean;\r\n  isBrowseLoading: boolean;\r\n  isFoldersLoading: boolean;\r\n  isRootFoldersLoading: boolean;\r\n  isUploading: boolean;\r\n  isCreatingFolder: boolean;\r\n  isDeleting: boolean;\r\n  isMoving: boolean;\r\n  isCopying: boolean;\r\n  \r\n  // Error states\r\n  error: any;\r\n  \r\n  // Actions\r\n  navigateToPath: (path: string) => void;\r\n  navigateUp: () => void;\r\n  selectItem: (relativePath: string) => void;\r\n  selectAll: () => void;\r\n  clearSelection: () => void;\r\n  setViewMode: (mode: ViewMode) => void;\r\n  updateFilters: (newFilters: Partial<FileFilters>) => void;\r\n  resetFilters: () => void;\r\n  uploadFiles: (files: File[], folderPath?: string) => void;\r\n  createFolder: (folderName: string, parentPath?: string) => void;\r\n  deleteSelectedItems: (permanent?: boolean) => void;\r\n  deleteItem: (relativePath: string, permanent?: boolean) => void;\r\n  moveSelectedItems: (destinationPath: string, overwriteExisting?: boolean) => void;\r\n  moveItem: (sourcePath: string, destinationPath: string, overwriteExisting?: boolean) => void;\r\n  copySelectedItems: (destinationPath: string, overwriteExisting?: boolean) => void;\r\n  copyItem: (sourcePath: string, destinationPath: string, overwriteExisting?: boolean) => void;\r\n  downloadFile: (relativePath: string) => void;\r\n  refetchBrowse: () => void;\r\n  refetchFolders: () => void;\r\n  refetchRootFolders: () => void;\r\n  \r\n  // Computed\r\n  hasSelection: boolean;\r\n  selectedCount: number;\r\n  canNavigateUp: boolean;\r\n}\r\n\r\n// Create context\r\nconst FileManagerContext = createContext<FileManagerContextType | null>(null);\r\n\r\n// Provider component\r\nexport const FileManagerProvider = ({ children }: { children: ReactNode }) => {\r\n  const toast = useToast();\r\n  const queryClient = useQueryClient();\r\n\r\n  // State\r\n  const [currentPath, setCurrentPath] = useState<string>(\"\");\r\n  const [selectedItems, setSelectedItems] = useState<string[]>([]);\r\n  const [viewMode, setViewMode] = useState<ViewMode>(\"grid\");\r\n  const [filters, setFilters] = useState<FileFilters>(defaultFilters);\r\n  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);\r\n\r\n  // Browse files query\r\n  const browseParams: BrowseParams = {\r\n    path: currentPath,\r\n    page: 1,\r\n    pageSize: 20,\r\n    search: filters.search || undefined,\r\n    fileType: filters.fileType !== \"all\" ? filters.fileType : undefined,\r\n    extension: filters.extension || undefined,\r\n    sortBy: filters.sortBy,\r\n    sortOrder: filters.sortOrder,\r\n    minSize: filters.sizeRange.min,\r\n    maxSize: filters.sizeRange.max,\r\n    fromDate: filters.dateRange.from,\r\n    toDate: filters.dateRange.to,\r\n  };\r\n\r\n  const {\r\n    data: browseData,\r\n    isLoading: isBrowseLoading,\r\n    error: browseError,\r\n    refetch: refetchBrowse,\r\n  } = useQuery({\r\n    queryKey: fileManagerKeys.browse(browseParams),\r\n    queryFn: () => fileManagerService.browse(browseParams),\r\n    staleTime: 30000, // 30 seconds\r\n  });\r\n\r\n  // Folder structure query\r\n  const {\r\n    data: folderStructure,\r\n    isLoading: isFoldersLoading,\r\n    refetch: refetchFolders,\r\n  } = useQuery({\r\n    queryKey: fileManagerKeys.folders(currentPath),\r\n    queryFn: () => fileManagerService.getFolderStructure(currentPath),\r\n    staleTime: 60000, // 1 minute\r\n  });\r\n\r\n  // Root folder structure query\r\n  const {\r\n    data: rootFolderStructure,\r\n    isLoading: isRootFoldersLoading,\r\n    refetch: refetchRootFolders\r\n  } = useQuery({\r\n    queryKey: fileManagerKeys.folders(\"\"),\r\n    queryFn: () => fileManagerService.getFolderStructure(\"\"),\r\n    staleTime: 60000, // 1 minute\r\n  });\r\n\r\n  // Upload mutation\r\n  const uploadMutation = useMutation({\r\n    mutationFn: async ({\r\n      files,\r\n      folderPath,\r\n      createThumbnails = true,\r\n      overwriteExisting = false,\r\n    }: {\r\n      files: File[];\r\n      folderPath?: string;\r\n      createThumbnails?: boolean;\r\n      overwriteExisting?: boolean;\r\n    }) => {\r\n      // Initialize upload progress\r\n      const initialProgress = files.map(file => ({\r\n        fileName: file.name,\r\n        progress: 0,\r\n        status: \"pending\" as const,\r\n      }));\r\n      setUploadProgress(initialProgress);\r\n\r\n      // Simulate progress updates (in real app, this would come from upload progress)\r\n      const progressInterval = setInterval(() => {\r\n        setUploadProgress(prev => \r\n          prev.map(item => ({\r\n            ...item,\r\n            progress: Math.min(item.progress + Math.random() * 20, 90),\r\n            status: item.progress < 90 ? \"uploading\" as const : item.status,\r\n          }))\r\n        );\r\n      }, 500);\r\n\r\n      try {\r\n        const result = await fileManagerService.uploadFiles(\r\n          files,\r\n          folderPath || currentPath,\r\n          createThumbnails,\r\n          overwriteExisting\r\n        );\r\n\r\n        clearInterval(progressInterval);\r\n        \r\n        // Mark as completed\r\n        setUploadProgress(prev => \r\n          prev.map(item => ({\r\n            ...item,\r\n            progress: 100,\r\n            status: \"completed\" as const,\r\n          }))\r\n        );\r\n\r\n        return result;\r\n      } catch (error) {\r\n        clearInterval(progressInterval);\r\n        \r\n        // Mark as error\r\n        setUploadProgress(prev => \r\n          prev.map(item => ({\r\n            ...item,\r\n            status: \"error\" as const,\r\n            error: error instanceof Error ? error.message : \"Upload failed\",\r\n          }))\r\n        );\r\n        \r\n        throw error;\r\n      }\r\n    },\r\n    onSuccess: (data) => {\r\n      toast.success({\r\n        title: \"Upload successful\",\r\n        description: `${data.successCount} files uploaded successfully`,\r\n      });\r\n      \r\n      // Invalidate queries\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.browse(browseParams) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(currentPath) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(\"\") }); // Refetch root folders\r\n      \r\n      // Clear upload progress after delay\r\n      setTimeout(() => setUploadProgress([]), 3000);\r\n    },\r\n    onError: (error) => {\r\n      toast.error({\r\n        title: \"Upload failed\",\r\n        description: error instanceof Error ? error.message : \"Failed to upload files\",\r\n      });\r\n    },\r\n  });\r\n\r\n  // Create folder mutation\r\n  const createFolderMutation = useMutation({\r\n    mutationFn: (request: CreateFolderRequest) => fileManagerService.createFolder(request),\r\n    onSuccess: (data) => {\r\n      toast.success({\r\n        title: \"Folder created\",\r\n        description: `Folder \"${data.name}\" created successfully`,\r\n      });\r\n      \r\n      // Invalidate queries\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.browse(browseParams) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(currentPath) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(\"\") }); // Refetch root folders\r\n    },\r\n    onError: (error) => {\r\n      toast.error({\r\n        title: \"Failed to create folder\",\r\n        description: error instanceof Error ? error.message : \"Unknown error\",\r\n      });\r\n    },\r\n  });\r\n\r\n  // Delete mutation\r\n  const deleteMutation = useMutation({\r\n    mutationFn: (request: DeleteRequest) => fileManagerService.deleteFiles(request),\r\n    onSuccess: (data) => {\r\n      toast.success({\r\n        title: \"Files deleted\",\r\n        description: `${data.successCount} items deleted successfully`,\r\n      });\r\n      \r\n      // Clear selection\r\n      setSelectedItems([]);\r\n      \r\n      // Invalidate queries\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.browse(browseParams) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(currentPath) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(\"\") }); // Refetch root folders\r\n    },\r\n    onError: (error) => {\r\n      toast.error({\r\n        title: \"Failed to delete files\",\r\n        description: error instanceof Error ? error.message : \"Unknown error\",\r\n      });\r\n    },\r\n  });\r\n\r\n  // Move mutation\r\n  const moveMutation = useMutation({\r\n    mutationFn: (request: MoveRequest) => fileManagerService.moveFiles(request),\r\n    onSuccess: (data) => {\r\n      toast.success({\r\n        title: \"Files moved\",\r\n        description: `${data.successCount} items moved successfully`,\r\n      });\r\n      \r\n      // Clear selection\r\n      setSelectedItems([]);\r\n      \r\n      // Invalidate queries\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.all });\r\n    },\r\n    onError: (error) => {\r\n      toast.error({\r\n        title: \"Failed to move files\",\r\n        description: error instanceof Error ? error.message : \"Unknown error\",\r\n      });\r\n    },\r\n  });\r\n\r\n  // Copy mutation\r\n  const copyMutation = useMutation({\r\n    mutationFn: (request: MoveRequest) => fileManagerService.copyFiles(request),\r\n    onSuccess: (data) => {\r\n      toast.success({\r\n        title: \"Files copied\",\r\n        description: `${data.successCount} items copied successfully`,\r\n      });\r\n      \r\n      // Clear selection\r\n      setSelectedItems([]);\r\n      \r\n      // Invalidate queries\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.browse(browseParams) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(currentPath) });\r\n      queryClient.invalidateQueries({ queryKey: fileManagerKeys.folders(\"\") }); // Refetch root folders\r\n    },\r\n    onError: (error) => {\r\n      toast.error({\r\n        title: \"Failed to copy files\",\r\n        description: error instanceof Error ? error.message : \"Unknown error\",\r\n      });\r\n    },\r\n  });\r\n\r\n  // Actions\r\n  const navigateToPath = useCallback((path: string) => {\r\n    setCurrentPath(path);\r\n    setSelectedItems([]);\r\n  }, []);\r\n\r\n  const navigateUp = useCallback(() => {\r\n    if (currentPath) {\r\n      const parentPath = currentPath.split(\"/\").slice(0, -1).join(\"/\");\r\n      navigateToPath(parentPath);\r\n    }\r\n  }, [currentPath, navigateToPath]);\r\n\r\n  const selectItem = useCallback((relativePath: string) => {\r\n    setSelectedItems(prevSelected => {\r\n      if (prevSelected.includes(relativePath)) {\r\n        return prevSelected.filter(item => item !== relativePath);\r\n      }\r\n      return [...prevSelected, relativePath];\r\n    });\r\n  }, []);\r\n\r\n  const selectAll = useCallback(() => {\r\n    if (browseData?.items) {\r\n      const allPaths = browseData.items.map(item => item.relativePath);\r\n      setSelectedItems(allPaths);\r\n    }\r\n  }, [browseData?.items]);\r\n\r\n  const clearSelection = useCallback(() => {\r\n    setSelectedItems([]);\r\n  }, []);\r\n\r\n  const updateFilters = useCallback((newFilters: Partial<FileFilters>) => {\r\n    setFilters(prev => ({ ...prev, ...newFilters }));\r\n  }, []);\r\n\r\n  const resetFilters = useCallback(() => {\r\n    setFilters(defaultFilters);\r\n  }, []);\r\n\r\n  const uploadFiles = useCallback((files: File[], folderPath?: string) => {\r\n    uploadMutation.mutate({ files, folderPath });\r\n  }, [uploadMutation]);\r\n\r\n  const createFolder = useCallback((folderName: string, parentPath?: string) => {\r\n    createFolderMutation.mutate({\r\n      parentPath: parentPath || currentPath,\r\n      folderName,\r\n    });\r\n  }, [createFolderMutation, currentPath]);\r\n\r\n  const deleteSelectedItems = useCallback((permanent = false) => {\r\n    setSelectedItems(currentItems => {\r\n      \r\n      if (currentItems.length > 0) {\r\n        deleteMutation.mutate({\r\n          filePaths: currentItems,\r\n          permanent,\r\n        });\r\n      }\r\n      \r\n      return currentItems; // Keep items until mutation succeeds\r\n    });\r\n  }, [deleteMutation]);\r\n\r\n  const deleteItem = useCallback((relativePath: string, permanent = false) => {\r\n    if (relativePath) {\r\n      deleteMutation.mutate({\r\n        filePaths: [relativePath],\r\n        permanent,\r\n      });\r\n    }\r\n  }, [deleteMutation]);\r\n\r\n  const moveSelectedItems = useCallback((destinationPath: string, overwriteExisting = false) => {\r\n    setSelectedItems(currentItems => {\r\n      if (currentItems.length > 0) {\r\n        moveMutation.mutate({\r\n          sourcePaths: currentItems,\r\n          destinationPath,\r\n          overwriteExisting,\r\n        });\r\n      }\r\n      return currentItems;\r\n    });\r\n  }, [moveMutation]);\r\n\r\n  const moveItem = useCallback((sourcePath: string, destinationPath: string, overwriteExisting = false) => {\r\n    if (sourcePath) {\r\n      moveMutation.mutate({\r\n        sourcePaths: [sourcePath],\r\n        destinationPath,\r\n        overwriteExisting,\r\n      });\r\n    }\r\n  }, [moveMutation]);\r\n\r\n  const copySelectedItems = useCallback((destinationPath: string, overwriteExisting = false) => {\r\n    setSelectedItems(currentItems => {\r\n      if (currentItems.length > 0) {\r\n        copyMutation.mutate({\r\n          sourcePaths: currentItems,\r\n          destinationPath,\r\n          overwriteExisting,\r\n        });\r\n      }\r\n      return currentItems;\r\n    });\r\n  }, [copyMutation]);\r\n\r\n  const copyItem = useCallback((sourcePath: string, destinationPath: string, overwriteExisting = false) => {\r\n    if (sourcePath) {\r\n      copyMutation.mutate({\r\n        sourcePaths: [sourcePath],\r\n        destinationPath,\r\n        overwriteExisting,\r\n      });\r\n    }\r\n  }, [copyMutation]);\r\n\r\n  // Download file action\r\n  const downloadFile = useCallback(async (relativePath: string) => {\r\n    try {\r\n      const fileName = relativePath.split('/').pop() || 'download';\r\n\r\n      toast.success({\r\n        title: \"Download Started\",\r\n        description: `Downloading ${fileName}...`,\r\n      });\r\n\r\n      // Use fetchWithAuth to download the file with authentication\r\n      const downloadUrl = fileManagerService.getDownloadUrl(relativePath);\r\n      const response = await fetchWithAuth(downloadUrl);\r\n      \r\n      if (!response.ok) {\r\n        throw new Error(`Download failed: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      // Get the file blob\r\n      const blob = await response.blob();\r\n      \r\n      // Create blob URL and trigger download\r\n      const blobUrl = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = blobUrl;\r\n      link.setAttribute('download', fileName);\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      \r\n      // Clean up the blob URL\r\n      window.URL.revokeObjectURL(blobUrl);\r\n\r\n      toast.success({\r\n        title: \"Download Complete\",\r\n        description: `${fileName} downloaded successfully.`,\r\n      });\r\n    } catch (error) {\r\n      toast.error({\r\n        title: \"Download Failed\",\r\n        description: error instanceof Error ? error.message : \"Could not download file.\",\r\n      });\r\n      console.error(\"Error downloading file:\", error);\r\n    }\r\n  }, [toast]);\r\n\r\n  // Context value\r\n  const value: FileManagerContextType = {\r\n    // Data\r\n    browseData,\r\n    folderStructure,\r\n    rootFolderStructure,\r\n    \r\n    // State\r\n    currentPath,\r\n    selectedItems,\r\n    viewMode,\r\n    filters,\r\n    uploadProgress,\r\n    \r\n    // Loading states\r\n    isLoading: isBrowseLoading || isFoldersLoading || isRootFoldersLoading,\r\n    isBrowseLoading,\r\n    isFoldersLoading,\r\n    isRootFoldersLoading,\r\n    isUploading: uploadMutation.isPending,\r\n    isCreatingFolder: createFolderMutation.isPending,\r\n    isDeleting: deleteMutation.isPending,\r\n    isMoving: moveMutation.isPending,\r\n    isCopying: copyMutation.isPending,\r\n    \r\n    // Error states\r\n    error: browseError,\r\n    \r\n    // Actions\r\n    navigateToPath,\r\n    navigateUp,\r\n    selectItem,\r\n    selectAll,\r\n    clearSelection,\r\n    setViewMode,\r\n    updateFilters,\r\n    resetFilters,\r\n    uploadFiles,\r\n    createFolder,\r\n    deleteSelectedItems,\r\n    deleteItem,\r\n    moveSelectedItems,\r\n    moveItem,\r\n    copySelectedItems,\r\n    copyItem,\r\n    downloadFile,\r\n    refetchBrowse,\r\n    refetchFolders,\r\n    refetchRootFolders,\r\n    \r\n    // Computed\r\n    hasSelection: selectedItems.length > 0,\r\n    selectedCount: selectedItems.length,\r\n    canNavigateUp: !!currentPath,\r\n  };\r\n\r\n  return (\r\n    <FileManagerContext.Provider value={value}>\r\n      {children}\r\n    </FileManagerContext.Provider>\r\n  );\r\n};\r\n\r\n// Hook to use the context\r\nexport const useFileManagerContext = () => {\r\n  const context = useContext(FileManagerContext);\r\n  if (!context) {\r\n    throw new Error('useFileManagerContext must be used within a FileManagerProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAID;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAkBO,MAAM,kBAAkB;IAC7B,KAAK;QAAC;KAAc;IACpB,QAAQ,CAAC,SAAyB;eAAI,gBAAgB,GAAG;YAAE;YAAU;SAAO;IAC5E,SAAS,CAAC,WAAqB;eAAI,gBAAgB,GAAG;YAAE;YAAW;SAAS;IAC5E,UAAU,CAAC,WAAqB;eAAI,gBAAgB,GAAG;YAAE;YAAY;SAAS;AAChF;AAEA,kBAAkB;AAClB,MAAM,iBAA8B;IAClC,QAAQ;IACR,UAAU;IACV,WAAW;IACX,QAAQ;IACR,WAAW;IACX,WAAW,CAAC;IACZ,WAAW,CAAC;AACd;AA0DA,iBAAiB;AACjB,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAGjE,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAA2B;;IACvE,MAAM,QAAQ,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,QAAQ;IACR,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAEzE,qBAAqB;IACrB,MAAM,eAA6B;QACjC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ,QAAQ,MAAM,IAAI;QAC1B,UAAU,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,GAAG;QAC1D,WAAW,QAAQ,SAAS,IAAI;QAChC,QAAQ,QAAQ,MAAM;QACtB,WAAW,QAAQ,SAAS;QAC5B,SAAS,QAAQ,SAAS,CAAC,GAAG;QAC9B,SAAS,QAAQ,SAAS,CAAC,GAAG;QAC9B,UAAU,QAAQ,SAAS,CAAC,IAAI;QAChC,QAAQ,QAAQ,SAAS,CAAC,EAAE;IAC9B;IAEA,MAAM,EACJ,MAAM,UAAU,EAChB,WAAW,eAAe,EAC1B,OAAO,WAAW,EAClB,SAAS,aAAa,EACvB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU,gBAAgB,MAAM,CAAC;QACjC,OAAO;4CAAE,IAAM,0HAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;;QACzC,WAAW;IACb;IAEA,yBAAyB;IACzB,MAAM,EACJ,MAAM,eAAe,EACrB,WAAW,gBAAgB,EAC3B,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU,gBAAgB,OAAO,CAAC;QAClC,OAAO;4CAAE,IAAM,0HAAA,CAAA,qBAAkB,CAAC,kBAAkB,CAAC;;QACrD,WAAW;IACb;IAEA,8BAA8B;IAC9B,MAAM,EACJ,MAAM,mBAAmB,EACzB,WAAW,oBAAoB,EAC/B,SAAS,kBAAkB,EAC5B,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU,gBAAgB,OAAO,CAAC;QAClC,OAAO;4CAAE,IAAM,0HAAA,CAAA,qBAAkB,CAAC,kBAAkB,CAAC;;QACrD,WAAW;IACb;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU;+DAAE,OAAO,EACjB,KAAK,EACL,UAAU,EACV,mBAAmB,IAAI,EACvB,oBAAoB,KAAK,EAM1B;gBACC,6BAA6B;gBAC7B,MAAM,kBAAkB,MAAM,GAAG;uFAAC,CAAA,OAAQ,CAAC;4BACzC,UAAU,KAAK,IAAI;4BACnB,UAAU;4BACV,QAAQ;wBACV,CAAC;;gBACD,kBAAkB;gBAElB,gFAAgF;gBAChF,MAAM,mBAAmB;wFAAY;wBACnC;gGAAkB,CAAA,OAChB,KAAK,GAAG;wGAAC,CAAA,OAAQ,CAAC;4CAChB,GAAG,IAAI;4CACP,UAAU,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI;4CACvD,QAAQ,KAAK,QAAQ,GAAG,KAAK,cAAuB,KAAK,MAAM;wCACjE,CAAC;;;oBAEL;uFAAG;gBAEH,IAAI;oBACF,MAAM,SAAS,MAAM,0HAAA,CAAA,qBAAkB,CAAC,WAAW,CACjD,OACA,cAAc,aACd,kBACA;oBAGF,cAAc;oBAEd,oBAAoB;oBACpB;2EAAkB,CAAA,OAChB,KAAK,GAAG;mFAAC,CAAA,OAAQ,CAAC;wCAChB,GAAG,IAAI;wCACP,UAAU;wCACV,QAAQ;oCACV,CAAC;;;oBAGH,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,cAAc;oBAEd,gBAAgB;oBAChB;2EAAkB,CAAA,OAChB,KAAK,GAAG;mFAAC,CAAA,OAAQ,CAAC;wCAChB,GAAG,IAAI;wCACP,QAAQ;wCACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oCAClD,CAAC;;;oBAGH,MAAM;gBACR;YACF;;QACA,SAAS;+DAAE,CAAC;gBACV,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,GAAG,KAAK,YAAY,CAAC,4BAA4B,CAAC;gBACjE;gBAEA,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,MAAM,CAAC;gBAAc;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAa;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAI,IAAI,uBAAuB;gBAEjG,oCAAoC;gBACpC;uEAAW,IAAM,kBAAkB,EAAE;sEAAG;YAC1C;;QACA,OAAO;+DAAE,CAAC;gBACR,MAAM,KAAK,CAAC;oBACV,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACvC,UAAU;qEAAE,CAAC,UAAiC,0HAAA,CAAA,qBAAkB,CAAC,YAAY,CAAC;;QAC9E,SAAS;qEAAE,CAAC;gBACV,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,sBAAsB,CAAC;gBAC3D;gBAEA,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,MAAM,CAAC;gBAAc;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAa;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAI,IAAI,uBAAuB;YACnG;;QACA,OAAO;qEAAE,CAAC;gBACR,MAAM,KAAK,CAAC;oBACV,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU;+DAAE,CAAC,UAA2B,0HAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;;QACvE,SAAS;+DAAE,CAAC;gBACV,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,GAAG,KAAK,YAAY,CAAC,2BAA2B,CAAC;gBAChE;gBAEA,kBAAkB;gBAClB,iBAAiB,EAAE;gBAEnB,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,MAAM,CAAC;gBAAc;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAa;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAI,IAAI,uBAAuB;YACnG;;QACA,OAAO;+DAAE,CAAC;gBACR,MAAM,KAAK,CAAC;oBACV,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,UAAU;6DAAE,CAAC,UAAyB,0HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;;QACnE,SAAS;6DAAE,CAAC;gBACV,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,GAAG,KAAK,YAAY,CAAC,yBAAyB,CAAC;gBAC9D;gBAEA,kBAAkB;gBAClB,iBAAiB,EAAE;gBAEnB,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,GAAG;gBAAC;YAChE;;QACA,OAAO;6DAAE,CAAC;gBACR,MAAM,KAAK,CAAC;oBACV,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,UAAU;6DAAE,CAAC,UAAyB,0HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;;QACnE,SAAS;6DAAE,CAAC;gBACV,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,GAAG,KAAK,YAAY,CAAC,0BAA0B,CAAC;gBAC/D;gBAEA,kBAAkB;gBAClB,iBAAiB,EAAE;gBAEnB,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,MAAM,CAAC;gBAAc;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAa;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,gBAAgB,OAAO,CAAC;gBAAI,IAAI,uBAAuB;YACnG;;QACA,OAAO;6DAAE,CAAC;gBACR,MAAM,KAAK,CAAC;oBACV,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;;IACF;IAEA,UAAU;IACV,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAClC,eAAe;YACf,iBAAiB,EAAE;QACrB;0DAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC7B,IAAI,aAAa;gBACf,MAAM,aAAa,YAAY,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;gBAC5D,eAAe;YACjB;QACF;sDAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YAC9B;+DAAiB,CAAA;oBACf,IAAI,aAAa,QAAQ,CAAC,eAAe;wBACvC,OAAO,aAAa,MAAM;2EAAC,CAAA,OAAQ,SAAS;;oBAC9C;oBACA,OAAO;2BAAI;wBAAc;qBAAa;gBACxC;;QACF;sDAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC5B,IAAI,YAAY,OAAO;gBACrB,MAAM,WAAW,WAAW,KAAK,CAAC,GAAG;2EAAC,CAAA,OAAQ,KAAK,YAAY;;gBAC/D,iBAAiB;YACnB;QACF;qDAAG;QAAC,YAAY;KAAM;IAEtB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACjC,iBAAiB,EAAE;QACrB;0DAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACjC;kEAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,GAAG,UAAU;oBAAC,CAAC;;QAChD;yDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC/B,WAAW;QACb;wDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,OAAe;YAC9C,eAAe,MAAM,CAAC;gBAAE;gBAAO;YAAW;QAC5C;uDAAG;QAAC;KAAe;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,YAAoB;YACpD,qBAAqB,MAAM,CAAC;gBAC1B,YAAY,cAAc;gBAC1B;YACF;QACF;wDAAG;QAAC;QAAsB;KAAY;IAEtC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE,CAAC,YAAY,KAAK;YACxD;wEAAiB,CAAA;oBAEf,IAAI,aAAa,MAAM,GAAG,GAAG;wBAC3B,eAAe,MAAM,CAAC;4BACpB,WAAW;4BACX;wBACF;oBACF;oBAEA,OAAO,cAAc,qCAAqC;gBAC5D;;QACF;+DAAG;QAAC;KAAe;IAEnB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,cAAsB,YAAY,KAAK;YACrE,IAAI,cAAc;gBAChB,eAAe,MAAM,CAAC;oBACpB,WAAW;wBAAC;qBAAa;oBACzB;gBACF;YACF;QACF;sDAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC,iBAAyB,oBAAoB,KAAK;YACvF;sEAAiB,CAAA;oBACf,IAAI,aAAa,MAAM,GAAG,GAAG;wBAC3B,aAAa,MAAM,CAAC;4BAClB,aAAa;4BACb;4BACA;wBACF;oBACF;oBACA,OAAO;gBACT;;QACF;6DAAG;QAAC;KAAa;IAEjB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,YAAoB,iBAAyB,oBAAoB,KAAK;YAClG,IAAI,YAAY;gBACd,aAAa,MAAM,CAAC;oBAClB,aAAa;wBAAC;qBAAW;oBACzB;oBACA;gBACF;YACF;QACF;oDAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC,iBAAyB,oBAAoB,KAAK;YACvF;sEAAiB,CAAA;oBACf,IAAI,aAAa,MAAM,GAAG,GAAG;wBAC3B,aAAa,MAAM,CAAC;4BAClB,aAAa;4BACb;4BACA;wBACF;oBACF;oBACA,OAAO;gBACT;;QACF;6DAAG;QAAC;KAAa;IAEjB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,YAAoB,iBAAyB,oBAAoB,KAAK;YAClG,IAAI,YAAY;gBACd,aAAa,MAAM,CAAC;oBAClB,aAAa;wBAAC;qBAAW;oBACzB;oBACA;gBACF;YACF;QACF;oDAAG;QAAC;KAAa;IAEjB,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YACtC,IAAI;gBACF,MAAM,WAAW,aAAa,KAAK,CAAC,KAAK,GAAG,MAAM;gBAElD,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,CAAC,YAAY,EAAE,SAAS,GAAG,CAAC;gBAC3C;gBAEA,6DAA6D;gBAC7D,MAAM,cAAc,0HAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBACtD,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;gBAErC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBAC9E;gBAEA,oBAAoB;gBACpB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,uCAAuC;gBACvC,MAAM,UAAU,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3C,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,YAAY,CAAC,YAAY;gBAC9B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,wBAAwB;gBACxB,OAAO,GAAG,CAAC,eAAe,CAAC;gBAE3B,MAAM,OAAO,CAAC;oBACZ,OAAO;oBACP,aAAa,GAAG,SAAS,yBAAyB,CAAC;gBACrD;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,KAAK,CAAC;oBACV,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;gBACA,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;wDAAG;QAAC;KAAM;IAEV,gBAAgB;IAChB,MAAM,QAAgC;QACpC,OAAO;QACP;QACA;QACA;QAEA,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB,WAAW,mBAAmB,oBAAoB;QAClD;QACA;QACA;QACA,aAAa,eAAe,SAAS;QACrC,kBAAkB,qBAAqB,SAAS;QAChD,YAAY,eAAe,SAAS;QACpC,UAAU,aAAa,SAAS;QAChC,WAAW,aAAa,SAAS;QAEjC,eAAe;QACf,OAAO;QAEP,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,WAAW;QACX,cAAc,cAAc,MAAM,GAAG;QACrC,eAAe,cAAc,MAAM;QACnC,eAAe,CAAC,CAAC;IACnB;IAEA,qBACE,6LAAC,mBAAmB,QAAQ;QAAC,OAAO;kBACjC;;;;;;AAGP;GAxda;;QACG,wHAAA,CAAA,WAAQ;QACF,yLAAA,CAAA,iBAAc;QA8B9B,8KAAA,CAAA,WAAQ;QAWR,8KAAA,CAAA,WAAQ;QAWR,8KAAA,CAAA,WAAQ;QAOW,iLAAA,CAAA,cAAW;QAyFL,iLAAA,CAAA,cAAW;QAsBjB,iLAAA,CAAA,cAAW;QAyBb,iLAAA,CAAA,cAAW;QAuBX,iLAAA,CAAA,cAAW;;;KA5NrB;AA2dN,MAAM,wBAAwB;;IACnC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 2184, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2381, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,odACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/components/ui/confirmation-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { createContext, useState, use<PERSON><PERSON>back, useContext, type ReactNode } from \"react\"\r\nimport {\r\n  <PERSON>alog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { AlertCircle, CheckCircle, Loader2 } from \"lucide-react\"\r\nimport { useToast } from \"@/hooks/use-toast\"\r\n\r\n// Types\r\ntype ConfirmationOptions = {\r\n  title: string\r\n  message: string\r\n  confirmText?: string\r\n  cancelText?: string\r\n  variant?: \"default\" | \"destructive\"\r\n  onConfirm?: () => Promise<void> | void\r\n  onCancel?: () => void\r\n}\r\n\r\ntype ConfirmationDialogContextType = {\r\n  confirm: (options: ConfirmationOptions) => Promise<boolean>\r\n  closeDialog: () => void\r\n  options: ConfirmationOptions | null\r\n  isOpen: boolean\r\n  isLoading: boolean\r\n  status: \"idle\" | \"loading\" | \"success\" | \"error\"\r\n  error: string | null\r\n}\r\n\r\n// Context\r\nconst ConfirmationDialogContext = createContext<ConfirmationDialogContextType | undefined>(undefined)\r\n\r\n// Provider Component\r\nexport function ConfirmationDialogProvider({ children }: { children: ReactNode }) {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [options, setOptions] = useState<ConfirmationOptions | null>(null)\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [status, setStatus] = useState<\"idle\" | \"loading\" | \"success\" | \"error\">(\"idle\")\r\n  const [error, setError] = useState<string | null>(null)\r\n  const [resolveRef, setResolveRef] = useState<((value: boolean) => void) | null>(null)\r\n  const { success, error: toastError } = useToast()\r\n\r\n  const confirm = useCallback((options: ConfirmationOptions) => {\r\n    setOptions(options)\r\n    setIsOpen(true)\r\n    setStatus(\"idle\")\r\n    setError(null)\r\n\r\n    return new Promise<boolean>((resolve) => {\r\n      setResolveRef(() => resolve)\r\n    })\r\n  }, [])\r\n\r\n  const handleConfirm = useCallback(async () => {\r\n    if (!options?.onConfirm) {\r\n      if (resolveRef) resolveRef(true)\r\n      setIsOpen(false)\r\n      return\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true)\r\n      setStatus(\"loading\")\r\n      await options.onConfirm()\r\n      setStatus(\"success\")\r\n      success({\r\n        title: \"Success\",\r\n        description: \"Operation completed successfully!\"\r\n      })\r\n      if (resolveRef) resolveRef(true)\r\n      setTimeout(() => {\r\n        setIsOpen(false)\r\n        setIsLoading(false)\r\n      }, 1000)\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : \"An error occurred\"\r\n      setStatus(\"error\")\r\n      setError(errorMessage)\r\n      toastError({\r\n        title: \"Error\",\r\n        description: `Error: ${errorMessage}`\r\n      })\r\n      setIsLoading(false)\r\n      if (resolveRef) resolveRef(false)\r\n    }\r\n  }, [options, resolveRef, success, toastError])\r\n\r\n  const handleCancel = useCallback(() => {\r\n    if (options?.onCancel) {\r\n      options.onCancel()\r\n    }\r\n    if (resolveRef) resolveRef(false)\r\n    setIsOpen(false)\r\n  }, [options, resolveRef])\r\n\r\n  const closeDialog = useCallback(() => {\r\n    if (isLoading) return\r\n    setIsOpen(false)\r\n    if (resolveRef) resolveRef(false)\r\n  }, [isLoading, resolveRef])\r\n\r\n  // Dialog Component\r\n  const ConfirmationDialogComponent = useCallback(() => {\r\n    if (!options) return null\r\n\r\n    return (\r\n      <Dialog open={isOpen} onOpenChange={(open) => !open && closeDialog()}>\r\n        <DialogContent className=\"sm:max-w-[425px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>{options.title}</DialogTitle>\r\n            <DialogDescription>{options.message}</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          {status === \"error\" && (\r\n            <div className=\"flex items-center gap-2 p-3 text-sm border rounded-md bg-destructive/10 text-destructive border-destructive/20\">\r\n              <AlertCircle className=\"w-4 h-4\" />\r\n              <span>{error || \"An error occurred\"}</span>\r\n            </div>\r\n          )}\r\n\r\n          {status === \"success\" && (\r\n            <div className=\"flex items-center gap-2 p-3 text-sm border rounded-md bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900/30\">\r\n              <CheckCircle className=\"w-4 h-4\" />\r\n              <span>Operation completed successfully</span>\r\n            </div>\r\n          )}\r\n\r\n          <DialogFooter className=\"gap-2\">\r\n            <Button variant=\"outline\" onClick={handleCancel} disabled={isLoading}>\r\n              {options.cancelText || \"Cancel\"}\r\n            </Button>\r\n            <Button\r\n              variant={options.variant || \"default\"}\r\n              onClick={handleConfirm}\r\n              disabled={isLoading || status === \"success\"}\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                  Processing...\r\n                </>\r\n              ) : (\r\n                options.confirmText || \"Confirm\"\r\n              )}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    )\r\n  }, [isOpen, options, status, error, isLoading, closeDialog, handleCancel, handleConfirm])\r\n\r\n  return (\r\n    <ConfirmationDialogContext.Provider\r\n      value={{\r\n        confirm,\r\n        closeDialog,\r\n        options,\r\n        isOpen,\r\n        isLoading,\r\n        status,\r\n        error,\r\n      }}\r\n    >\r\n      {children}\r\n      <ConfirmationDialogComponent />\r\n    </ConfirmationDialogContext.Provider>\r\n  )\r\n}\r\n\r\n// Hook\r\nexport function useConfirmationDialog() {\r\n  const context = useContext(ConfirmationDialogContext)\r\n\r\n  if (context === undefined) {\r\n    throw new Error(\"useConfirmationDialog must be used within a ConfirmationDialogProvider\")\r\n  }\r\n\r\n  return context\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AACA;;;AAbA;;;;;;AAoCA,UAAU;AACV,MAAM,0CAA4B,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA6C;AAGpF,SAAS,2BAA2B,EAAE,QAAQ,EAA2B;;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IAC/E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAChF,MAAM,EAAE,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE9C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAC3B,WAAW;YACX,UAAU;YACV,UAAU;YACV,SAAS;YAET,OAAO,IAAI;mEAAiB,CAAC;oBAC3B;2EAAc,IAAM;;gBACtB;;QACF;0DAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YAChC,IAAI,CAAC,SAAS,WAAW;gBACvB,IAAI,YAAY,WAAW;gBAC3B,UAAU;gBACV;YACF;YAEA,IAAI;gBACF,aAAa;gBACb,UAAU;gBACV,MAAM,QAAQ,SAAS;gBACvB,UAAU;gBACV,QAAQ;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA,IAAI,YAAY,WAAW;gBAC3B;6EAAW;wBACT,UAAU;wBACV,aAAa;oBACf;4EAAG;YACL,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,UAAU;gBACV,SAAS;gBACT,WAAW;oBACT,OAAO;oBACP,aAAa,CAAC,OAAO,EAAE,cAAc;gBACvC;gBACA,aAAa;gBACb,IAAI,YAAY,WAAW;YAC7B;QACF;gEAAG;QAAC;QAAS;QAAY;QAAS;KAAW;IAE7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE;YAC/B,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ;YAClB;YACA,IAAI,YAAY,WAAW;YAC3B,UAAU;QACZ;+DAAG;QAAC;QAAS;KAAW;IAExB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YAC9B,IAAI,WAAW;YACf,UAAU;YACV,IAAI,YAAY,WAAW;QAC7B;8DAAG;QAAC;QAAW;KAAW;IAE1B,mBAAmB;IACnB,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+EAAE;YAC9C,IAAI,CAAC,SAAS,OAAO;YAErB,qBACE,6LAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,YAAY;2FAAE,CAAC,OAAS,CAAC,QAAQ;;0BACrD,cAAA,6LAAC,8HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,8HAAA,CAAA,eAAY;;8CACX,6LAAC,8HAAA,CAAA,cAAW;8CAAE,QAAQ,KAAK;;;;;;8CAC3B,6LAAC,8HAAA,CAAA,oBAAiB;8CAAE,QAAQ,OAAO;;;;;;;;;;;;wBAGpC,WAAW,yBACV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAM,SAAS;;;;;;;;;;;;wBAInB,WAAW,2BACV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAK;;;;;;;;;;;;sCAIV,6LAAC,8HAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;oCAAc,UAAU;8CACxD,QAAQ,UAAU,IAAI;;;;;;8CAEzB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,QAAQ,OAAO,IAAI;oCAC5B,SAAS;oCACT,UAAU,aAAa,WAAW;8CAEjC,0BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD,QAAQ,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;QAOrC;8EAAG;QAAC;QAAQ;QAAS;QAAQ;QAAO;QAAW;QAAa;QAAc;KAAc;IAExF,qBACE,6LAAC,0BAA0B,QAAQ;QACjC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;;YAEC;0BACD,6LAAC;;;;;;;;;;;AAGP;GAtIgB;;QAOyB,wHAAA,CAAA,WAAQ;;;KAPjC;AAyIT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;IARgB", "debugId": null}}, {"offset": {"line": 2745, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Personal%20Projects/Decor/FE/admin-site/lib/providers.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\nimport { SessionProvider } from 'next-auth/react' // Import SessionProvider\r\nimport { ReactNode, useState } from 'react'\r\nimport { ToastProvider } from '@/providers/ToastProvider'\r\nimport { CategoryStoreProvider } from '@/providers/CategoryStoreProvider'\r\nimport { FileManagerProvider } from '@/contexts/FileManagerContext'\r\nimport { ConfirmationDialogProvider } from '@/components/ui/confirmation-dialog'\r\n\r\nexport function Providers({ children }: { children: ReactNode }) {\r\n  const [queryClient] = useState(() => new QueryClient())\r\n\r\n  return (\r\n    <SessionProvider>\r\n      <QueryClientProvider client={queryClient}>\r\n        <CategoryStoreProvider>\r\n          <ToastProvider>\r\n            <FileManagerProvider>\r\n              <ConfirmationDialogProvider>\r\n                {children}\r\n              </ConfirmationDialogProvider>\r\n            </FileManagerProvider>\r\n          </ToastProvider>\r\n        </CategoryStoreProvider>\r\n      </QueryClientProvider>\r\n    </SessionProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA,8PAAkD,yBAAyB;AAC3E;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUO,SAAS,UAAU,EAAE,QAAQ,EAA2B;;IAC7D,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;8BAAE,IAAM,IAAI,gLAAA,CAAA,cAAW;;IAEpD,qBACE,6LAAC,iJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,6LAAC,sIAAA,CAAA,wBAAqB;0BACpB,cAAA,6LAAC,8HAAA,CAAA,gBAAa;8BACZ,cAAA,6LAAC,kIAAA,CAAA,sBAAmB;kCAClB,cAAA,6LAAC,8IAAA,CAAA,6BAA0B;sCACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GAlBgB;KAAA", "debugId": null}}]}