{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2ebd57a8._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_2c088799.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|login|$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|login|$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5P92S+88RqJ/ggBueVzL9I53QQcrp/6DNjWS0KU02q4=", "__NEXT_PREVIEW_MODE_ID": "3a2dc7a9a783d37b0247f7a097ef16da", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fe1737e6b4b30a4d446f6c75cd4fca4df56f0af77824114ba004f106f7695e89", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7ba055d78ad20f04960466d606ba0f22fbfec1662e42b93c9cf9f0801e67a54a"}}}, "sortedMiddleware": ["/"], "functions": {}}